# Responsive Notification Widget

A modern, flexible notification widget system built with CSS Grid, Flexbox, and vanilla JavaScript. This replaces the original absolute-positioned notification system with a responsive, dynamic solution.

## Features

- ✅ **Responsive Design**: Uses CSS Grid and Flexbox instead of absolute positioning
- ✅ **Dynamic Theming**: Real-time color changes using CSS custom properties
- ✅ **Multiple Notification Types**: Stream, Combo, Live, Review notifications
- ✅ **Image & Initials Support**: Automatically handles images with fallback to initials
- ✅ **Toggle Controls**: Show/hide date and image sections
- ✅ **Smooth Animations**: Modern CSS animations for show/hide states
- ✅ **Mobile Responsive**: Adapts to different screen sizes
- ✅ **Auto-hide**: Configurable auto-hide functionality
- ✅ **Clean API**: Simple JavaScript API for easy integration

## Usage

### Basic Setup

```html
<!DOCTYPE html>
<html>
  <head>
    <link rel="stylesheet" href="notification-widget.css" />
  </head>
  <body>
    <!-- Your content -->

    <!-- Include the notification container -->
    <div id="notification-container" class="notification-container hidden">
      <!-- Widget content (see notification-widget.html for full structure) -->
    </div>

    <script src="notification-widget.js"></script>
  </body>
</html>
```

### JavaScript API

```javascript
// Create a notification widget instance
const widget = new NotificationWidget();

// Show a stream notification
widget.show({
  type: "stream",
  title: "John D. from New York",
  message: "just purchased Premium Plan",
  date: "5 minutes ago",
  image: "https://example.com/avatar.jpg",
  autoHide: true,
  autoHideDelay: 5000,
});

// Show notification with initials fallback
widget.show({
  type: "stream",
  title: "Sarah M. from London",
  message: "just signed up",
  date: "2 minutes ago",
  initials: "SM", // Will show if no image provided
});

// Show combo notification
widget.show({
  type: "combo",
  message: "people visited this page today",
  date: "2 hours ago",
  count: 1247,
  label: "visitors",
});

// Show live notification
widget.show({
  type: "live",
  title: "12",
  message: "people online",
  date: "Live",
  autoHide: false, // Stays visible
});

// Hide notification
widget.hide();
```

### Configuration Options

```javascript
// Update colors dynamically
widget.setColors({
  background: "#ffffff",
  title: "#7825f3",
  message: "#323232",
  date: "#737373",
});

// Toggle date visibility
widget.toggleDate(false); // Hide date section

// Toggle image visibility
widget.toggleImage(false); // Hide image/avatar section
```

### Notification Types

1. **Stream**: Shows user activity with avatar/initials
2. **Combo**: Shows counters with animated numbers
3. **Live**: Shows live activity with pulsing indicator
4. **Review**: Shows user reviews with quoted text

### Notification Object Properties

| Property        | Type    | Description                                             | Required            |
| --------------- | ------- | ------------------------------------------------------- | ------------------- |
| `type`          | string  | Notification type (`stream`, `combo`, `live`, `review`) | Yes                 |
| `title`         | string  | Main title text                                         | No                  |
| `message`       | string  | Message content                                         | No                  |
| `date`          | string  | Date/time text                                          | No                  |
| `image`         | string  | Avatar image URL                                        | No                  |
| `initials`      | string  | Fallback initials (if no image)                         | No                  |
| `count`         | number  | Number for combo notifications                          | No                  |
| `label`         | string  | Label for combo notifications                           | No                  |
| `autoHide`      | boolean | Whether to auto-hide                                    | No (default: true)  |
| `autoHideDelay` | number  | Auto-hide delay in ms                                   | No (default: 5000)  |
| `allowHTML`     | boolean | Allow HTML in message                                   | No (default: false) |

## CSS Architecture

The new system uses:

- **CSS Grid**: For main layout structure
- **Flexbox**: For internal component alignment
- **CSS Custom Properties**: For dynamic theming
- **Media Queries**: For responsive behavior
- **CSS Animations**: For smooth transitions

### Key CSS Classes

- `.notification-container`: Main container with positioning
- `.notification-widget`: Widget wrapper with styling
- `.notification-content`: Grid layout for content
- `.notification-icon`: Icon/avatar section
- `.notification-text`: Text content section
- `.notification-date`: Date and branding section

## Browser Support

- ✅ Chrome/Edge 57+
- ✅ Firefox 52+
- ✅ Safari 10.1+
- ✅ iOS Safari 10.3+
- ✅ Android Browser 81+

## Migration from Original Widget

Key improvements over the original widget:

1. **No Absolute Positioning**: Uses modern CSS layout
2. **Dynamic Resizing**: Automatically adapts to content
3. **Cleaner HTML**: Semantic structure without positioning hacks
4. **Better Mobile Support**: True responsive design
5. **Easier Customization**: CSS custom properties for theming
6. **Performance**: Hardware-accelerated animations
7. **Maintainability**: Cleaner, more readable code

## Examples

### Simple Integration

```javascript
// Quick notification
showNotification({
  type: "stream",
  title: "New User",
  message: "Someone just signed up!",
  date: "Just now",
  initials: "NU",
});
```

### Real-time Data

```javascript
// Update live counter
function updateLiveUsers(count) {
  widget.show({
    type: "live",
    title: count.toString(),
    message: "people online",
    date: "Live",
    autoHide: false,
  });
}
```

### Custom Styling

```css
/* Override default colors */
:root {
  --bg-color: #f8f9fa;
  --title-color: #dc3545;
  --message-color: #495057;
  --date-color: #6c757d;
}

/* Custom notification styles */
.notification-widget.custom {
  border: 2px solid var(--title-color);
  border-radius: 15px;
}
```

This new notification widget provides a solid foundation for modern, responsive notifications while maintaining all the functionality of the original system.
