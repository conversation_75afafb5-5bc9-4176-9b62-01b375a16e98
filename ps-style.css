#provesrc-debug-mode *,
#provesrc-notification-container * {
    box-sizing: border-box;
    line-height: 17px;
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100% !important;
    vertical-align: baseline;
    letter-spacing: 0px !important;
}

#provesrc-debug-mode .provesrc-logo > img {
    margin-left: auto;
    margin-right: auto;
}

#provesrc-notification-container .pfs-link svg {
    display: inline-block !important;
    width: 13px !important;
    height: 13px !important;
    margin-bottom: -2px;
}

#provesrc-debug-mode .debug-mode-container span.green-dot {
    display: inline-block;
    width: 9px;
    margin-right: 5px;
    height: 9px;
    background: #42ac05;
    border-radius: 50%;
}

#provesrc-debug-mode .debug-mode-container img {
    width: 35px;
    text-align: center;
}

#provesrc-debug-mode .debug-mode-container {
    text-align: center;
}

#provesrc-debug-mode .installed-note {
    margin-bottom: 2px;
    font-size: .9rem;
    color: #7205f7 !important;
}

#provesrc-debug-mode span.admin-note {
    color: #838383;
    font-weight: 400;
}

#provesrc-debug-mode {
    background-color: #fff;
    bottom: 46%;
    color: #7205f7 !important;
    left: -4px;
    font-size: .8rem;
    font-weight: bold;
    padding: 8px;
    border: 3px solid #7225f3;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

#provesrc-debug-mode,
#provesrc-widget-area {
    z-index: 2147483647;
}

#provesrc-debug-mode,
#provesrc-notification-container {
    position: fixed !important;
    font-family: Lato, arial, sans-serif !important;
}

/*COMBO RTL NOTIFICATION*/
#provesrc-notification-container .bubble-body.rtl-bubble.combo-type .bubble-icon {
    padding-right: 0 !important;
    padding-left: 5px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.combo-type .bubble-content {
    margin-right: auto;
    margin-left: 0;
    padding-left: 21px;
    padding-right: 0;
    font-family: Lato, arial, sans-serif !important;
}

/*RTL*/
#provesrc-notification-container .bubble-body.rtl-bubble {
    direction: rtl !important;
    text-align: right !important;
    font-family: Arial, sans-serif;
}

#provesrc-notification-container .bubble-body.rtl-bubble .bubble-content .bubble-cta .cta-arrow::after {
    border-right: 0;
    border-top: 0;
    border-left: 0.1em solid;
    border-bottom: 0.1em solid;
    margin-left: 0;
    margin-right: 0.3em;
}

#provesrc-notification-container .bubble-body.rtl-bubble .bubble-icon {
    border-right: 0;
    /*border-left: 1px solid #ebebeb;*/
}

#provesrc-notification-container .bubble-body.rtl-bubble #ps-bubble-close {
    left: 3px !important;
    right: inherit !important;
}

#provesrc-notification-container .bubble-body.rtl-bubble .bubble-content {
    display: flex;
    min-width: 0;
    margin-right: 0;
    padding: 7px;
    font-family: Lato, arial, sans-serif !important;
}

/*LTR*/

#provesrc-notification-container.bottom-center .bubble-body,
#provesrc-notification-container.top-left .bubble-body,
#provesrc-notification-container.bottom-left .bubble-body {
    float: left;
}

#provesrc-notification-container.top-right .bubble-body,
#provesrc-notification-container.bottom-right .bubble-body {
    float: right;
}

#provesrc-notification-container.bottom-center {
    left: 50%;
    bottom: 10px;
    margin-left: -165px;
}

#provesrc-notification-container.bottom-left {
    left: 10px;
    bottom: 10px;
}

#provesrc-notification-container.bottom-right {
    right: 10px;
    bottom: 10px;
}

#provesrc-notification-container.top-right {
    right: 10px;
    top: 10px;
}

#provesrc-notification-container.top-left {
    left: 10px;
    top: 10px;
}

#provesrc-notification-container .bubble-body:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, .2);
    -webkit-box-shadow: 0 6px 20px rgba(0, 0, 0, .2);
    -moz-box-shadow: 0 6px 20px rgba(0, 0, 0, .2);
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
    top: -10px;
}

#provesrc-notification-container .bubble-body {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer !important;
    direction: ltr !important;
    text-align: left !important;
    width: 330px;
    overflow: hidden;
    border-radius: 6px;
    background-color: #FFF;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15) !important;
    -webkit-box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15) !important;
    -moz-box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #ececec;
    font-family: Lato, arial, sans-serif !important;
    transition: all .3s;
    top: 0;
}

#provesrc-notification-container .bubble-content .bubble-cta .cta-arrow::after {
    position: relative;
    content: '';
    display: inline-block;
    width: 0.4em;
    height: 0.4em;
    border-right: 0.1em solid;
    border-top: 0.1em solid;
    transform: rotate(45deg) !important;
    margin-left: 0.3em;
    font-size: 17px;
    -webkit-animation: provesrc-arrow-bouncer 2s infinite;
    animation: provesrc-arrow-bouncer 2s infinite;
}

#provesrc-notification-container .bubble-content .bubble-cta {
    line-height: 17px;
    font-size: 12px !important;
    color: #a0a0a0;
    padding: 3px 0;
    border-radius: 5px;
    margin-bottom: 5px;
    margin-top: 6px;
    display: inline-block;
}


#provesrc-notification-container .bubble-body .bubble-content {
    min-width: 0;
    margin-left: 0;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body.stream-type .bubble-content {
    display: flex;
    padding: 7px;
    align-items: center;
}

#provesrc-notification-container .bubble-body.review-type .bubble-icon img {
    max-width: 55px;
    max-height: 60px;
}

#provesrc-notification-container .bubble-body .bubble-content-inner {
    flex: 1 1 0%;
    min-width: 0;
    margin: auto;
}

#provesrc-notification-container .bubble-body .bubble-content .bubble-time a,
#provesrc-notification-container .bubble-body .bubble-content .bubble-time span {
    display: inline !important;
    font-size: 11px !important;
    font-family: Lato, arial, sans-serif !important;
}


#provesrc-notification-container .bubble-body .bubble-content .bubble-time {
    display: flex;
    gap: 5px;
    font-size: 11px !important;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body.review-type .bubble-content .bubble-time {
    display: inline !important;
    gap: 5px;
    font-size: 11px !important;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body .bubble-content .bubble-description > span {
    font-size: 12px !important;
}

#provesrc-notification-container .bubble-body .bubble-content .bubble-description {
    line-height: 17px;
    font-size: 12px !important;
    margin-bottom: 2px;
    margin-top: 2px;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body .bubble-content .ps-bubble-title span {
    font-weight: 700;
    display: inline-block;
    padding: 3px 5px;
    border-radius: 3px;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body .bubble-content .ps-bubble-title {
    line-height: 17px;
    font-size: 15px !important;
    font-weight: bold;
    font-family: Lato, arial, sans-serif !important;
    margin-bottom: 2px;
}

#provesrc-notification-container .bubble-body .bubble-icon {
    min-width: 50px;
    min-height: 55px;
    display: flex;
    position: relative;
}

#provesrc-notification-container .bubble-body .bubble-icon svg {
    flex: 0 0 auto;
}

#provesrc-notification-container .bubble-body .bubble-time .pfs-link a {
    text-decoration: none;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body .bubble-time .pfs-link span.bubble-bolt > i {
    display: inline-block !important;
}

#provesrc-notification-container .bubble-body .bubble-time .pfs-link {
    display: inline-block;
    font-size: 11px !important;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .text-capitalize {
    text-transform: capitalize;
}

#provesrc-notification-container .text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

#provesrc-notification-container .no-text-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#provesrc-notification-container .mx-auto {
    margin-right: auto !important;
    margin-left: auto !important;
}

#provesrc-notification-container.ps-show {
    display: block;
}

#provesrc-notification-container.ps-hide {
    display: none;
}

#provesrc-notification-container #ps-bubble-close:hover {
    opacity: 0.8;
}

#provesrc-notification-container #ps-bubble-close {
    position: absolute;
    top: 3px;
    right: 3px;
    height: 13px;
    width: 13px;
    transform: rotate(45deg);
    cursor: pointer;
    opacity: 0.5;
    zoom: 1.3;
}

/*#provesrc-notification-container #ps-bubble-close:before {*/
/*content: '';*/
/*display: block;*/
/*width: 100%;*/
/*height: 3px;*/
/*background-color: #c4c4c4;*/
/*position: absolute;*/
/*left: 0;*/
/*top: 5px;*/
/*}*/

/*#provesrc-notification-container #ps-bubble-close:after {*/
/*content: '';*/
/*display: block;*/
/*height: 100%;*/
/*width: 3px;*/
/*background-color: #c4c4c4;*/
/*position: absolute;*/
/*left: 5px;*/
/*top: 0;*/
/*}*/

#provesrc-widget-area .ps-animated {
    -webkit-animation-duration: .5s;
    animation-duration: .5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

#provesrc-notification-container .no-visible {
    visibility: hidden;
}

#provesrc-notification-container .is-visible {
    visibility: visible;
}

/*STREAM NOTIFICATION*/
#provesrc-notification-container .bubble-body.stream-type a#ps-stream-product-link:hover {
    -webkit-text-decoration: underline !important;
    -moz-text-decoration: underline !important;
    -ms-text-decoration: underline !important;
    -o-text-decoration: underline !important;
    text-decoration: underline !important;
    cursor: pointer !important;
}

#provesrc-notification-container .bubble-body.stream-type a#ps-stream-product-link {
    font-weight: bold !important;
    color: #454545;
    -webkit-text-decoration: none;
    -moz-text-decoration: none;
    -ms-text-decoration: none;
    -o-text-decoration: none;
    text-decoration: none;
}

#provesrc-notification-container .bubble-body.stream-type a#ps-stream-no-product-link {
    font-weight: bold !important;
    color: #454545;
    -webkit-text-decoration: none;
    -moz-text-decoration: none;
    -ms-text-decoration: none;
    -o-text-decoration: none;
    text-decoration: none;
}


#provesrc-notification-container .bubble-body.stream-type .bubble-icon {
    border-right: none;
}

#provesrc-notification-container .bubble-body.stream-type span.stream-location > img.location-flag {
    max-width: 17px !important;
    width: 17px !important;
    border-radius: 2px !important;
    vertical-align: text-top !important;
    border: 1px solid #d2d2d2 !important;
    margin-left: 4px !important;
}

#provesrc-notification-container .bubble-body.stream-type .stream-location {
    font-size: 12px !important;
    opacity: 0.6 !important;
    font-weight: normal !important;
    color: inherit !important;
}

#provesrc-notification-container .bubble-body.review-type .bubble-icon span.bubble-initials,
#provesrc-notification-container .bubble-body.stream-type .bubble-icon span.bubble-initials {
    font-weight: bolder;
    font-size: 27px !important;
    align-content: center;
    height: 55px
}

#provesrc-notification-container .bubble-body.stream-type .bubble-icon img {
    max-width: 65px;
    max-height: 55px;
}

#provesrc-notification-container .bubble-body.stream-type .bubble-content .ps-bubble-title span {
    background-color: transparent;
    padding: 0 !important;
    max-width: 160px;
    vertical-align: middle;
}

#provesrc-notification-container .bubble-body.stream-type .bubble-content .bubble-description {
    margin-top: 0;
}

/*STREAM RTL*/
#provesrc-notification-container .bubble-body.stream-type.rtl-bubble {
    border-radius: inherit;
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

#provesrc-notification-container .bubble-body.stream-type.rtl-bubble .bubble-icon img {
    right: 2px;
}

#provesrc-notification-container .bubble-body.stream-type.rtl-bubble .bubble-icon {
    border-right: 0;
    border-left: none;
}

#provesrc-notification-container .bubble-body.review-type.rtl-bubble .bubble-icon span.bubble-initials,
#provesrc-notification-container .bubble-body.stream-type.rtl-bubble .bubble-icon span.bubble-initials {
    margin-right: 1px;
}

/*COMBO NOTIFICATION*/
#provesrc-notification-container .bubble-body.combo-type {
    background-color: #ffffff;
    width: 350px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.combo-type .bubble-icon {
    border-left: 0;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-icon {
    border-right: 0;
    width: 35%;
    padding-right: 5px;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-time {
    color: #7627f3;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-time .pfs-link a {
    color: #7627f3;
    text-decoration: none;
    font-weight: 700;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-time .pfs-link {
    padding: 1px 5px;
    background-color: #fff;
    border-radius: 2px;
    margin-top: 5px;
    border: 1px solid #7627f3;
    color: #7627f3;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-content.no-branding {
    padding-top: 20px;
    padding-bottom: 20px;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-content .bubble-description {
    color: #7627f3;
    font-size: 12px;
}

#provesrc-notification-container #ps-bubble-close > span.close-before {
    content: '';
    display: block;
    width: 100%;
    height: 3px;
    background-color: #c4c4c4;
    position: absolute;
    left: 0;
    top: 5px;
}

#provesrc-notification-container #ps-bubble-close > span.close-after {
    content: '';
    display: block;
    height: 100%;
    width: 3px;
    background-color: #c4c4c4;
    position: absolute;
    left: 5px;
    top: 0;
}

#provesrc-notification-container .bubble-body.combo-type #ps-bubble-close > span.close-after,
#provesrc-notification-container .bubble-body.combo-type #ps-bubble-close > span.close-before {
    background-color: #7627f3;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-content {
    margin-left: auto;
    padding-top: 10px;
    padding-right: 21px;
    padding-bottom: 10px;
    width: 65%;
    margin-top: 5px;
    margin-bottom: 5px;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-icon .combo-number > .refer {
    /*margin-top: -7px;*/
    font-weight: normal !important;
    font-size: 12px !important;
    /*font-size: 1vw;*/
}

#provesrc-notification-container .bubble-body.combo-type .bubble-icon .combo-number > .num-value {
    font-size: 28px !important;
    /*font-size: 2vw;*/
    font-weight: 900 !important;
    /*margin-top: -5px;*/
    line-height: 30px !important;
}

#provesrc-notification-container .scale {
    animation: pound 1s;
    animation-delay: 1s;
    animation-iteration-count: 3;
}

@keyframes pound {
    from {
        transform: none;
    }
    50% {
        transform: scale(1.2);
    }
    to {
        transform: none;
    }
}

.provesrc-arrow-bouncer {
    -webkit-animation: provesrc-arrow-bouncer 2s infinite;
    animation: provesrc-arrow-bouncer 2s infinite;
}

@-webkit-keyframes provesrc-arrow-bouncer {
    0%,
    20%,
    50%,
    80%,
    100% {
        -webkit-transform: translateX(0) rotate(45deg);
        transform: translateX(0) rotate(45deg);
    }
    40% {
        -webkit-transform: translateX(-5px) rotate(45deg);
        transform: translateX(-5px) rotate(45deg);
    }
    60% {
        -webkit-transform: translateX(-3px) rotate(45deg);
        transform: translateX(-3px) rotate(45deg);
    }
}

@keyframes provesrc-arrow-bouncer {
    0%,
    20%,
    50%,
    80%,
    100% {
        -webkit-transform: translateX(0) rotate(45deg);
        transform: translateX(0) rotate(45deg);
    }
    40% {
        -webkit-transform: translateX(-5px) rotate(45deg);
        transform: translateX(-5px) rotate(45deg);
    }
    60% {
        -webkit-transform: translateX(-3px) rotate(45deg);
        transform: translateX(-3px) rotate(45deg);
    }
}

#provesrc-notification-container .bubble-body.combo-type .bubble-icon .combo-number {
    position: relative;
    float: left;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #7627f3;
    width: 100%;
    text-align: center;
}

@-webkit-keyframes psFadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes psFadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.psFadeInUp {
    -webkit-animation-name: psFadeInUp;
    animation-name: psFadeInUp;
}

@-webkit-keyframes fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes psBounceInUp {
    from {
        opacity: 0.5;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.psBounceInUp {
    -webkit-animation-name: psBounceInUp;
    animation-name: psBounceInUp;
}

@-webkit-keyframes psBounceInDown {
    from {
        opacity: 0.5;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes psBounceInDown {
    from {
        opacity: 0.5;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.psBounceInDown {
    -webkit-animation-name: psBounceInDown;
    animation-name: psBounceInDown;
}

@-webkit-keyframes psFadeOutDown {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@keyframes psFadeOutDown {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

.psFadeOutDown {
    -webkit-animation-name: psFadeOutDown;
    animation-name: psFadeOutDown;
}

@-webkit-keyframes psFadeOutUp {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@keyframes psFadeOutUp {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

.psFadeOutUp {
    -webkit-animation-name: psFadeOutUp;
    animation-name: psFadeOutUp;
}

/*MOBILE WEB*/
@media only screen and (max-width: 815px) {
    #provesrc-notification-container {
        bottom: 0 !important;
        left: 0 !important;
        width: 100%;
        max-width: 100%;
    }

    #provesrc-notification-container._ps-mobile-small > .bubble-body {
        width: 100%;
        max-width: 75% !important;
        zoom: 0.8;
        border-radius: 0;
    }

    #provesrc-notification-container._ps-mobile-small > .review-type ._ps-review-rating > .review-source-container {
        margin-right: 2px !important;
    }

    #provesrc-notification-container._ps-mobile-small ._ps-review-rating > .review-source-container > span.review-source-name {
        display: none !important;
    }

    #provesrc-notification-container .bubble-body:hover {
        box-shadow: none;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        -webkit-transition: none;
        -moz-transition: none;
        -ms-transition: none;
        -o-transition: none;
        transition: none;
        top: 0;
    }

    #provesrc-notification-container .bubble-body.live-type,
    #provesrc-notification-container .bubble-body.combo-type {
        width: 100% !important;
    }

    #provesrc-notification-container > .bubble-body {
        width: 100%;
        max-width: 100%;
        border-radius: 0;
    }

    #provesrc-notification-container .bubble-content .bubble-cta {
        margin-bottom: 0;
        margin-top: 0;
    }

    #provesrc-notification-container.top-left,
    #provesrc-notification-container.top-right {
        right: 0 !important;
        top: 0 !important;
        height: 50px;
    }

    #provesrc-notification-container.bottom-center {
        margin-left: 0;
    }

    #provesrc-notification-container.bottom-center,
    #provesrc-notification-container.bottom-left,
    #provesrc-notification-container.bottom-right {
        right: 0 !important;
        bottom: 0 !important;
    }

    #provesrc-notification-container #ps-bubble-close span.close-after {
        height: 13px !important;
    }

    #provesrc-notification-container #ps-bubble-close span.close-before {
        width: 13px !important;
    }

    #provesrc-notification-container #ps-bubble-close {
        padding: 15px !important;
        top: 11px !important;
        right: -1px !important;
    }

    /*LIVE*/
    #provesrc-notification-container > .bubble-body.live-type > .bubble-live-pulse {
        float: left;
        width: 67px;
        display: block;
        position: absolute;
        border-right: none;
        height: 100%;
        margin-top: 0 !important;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-live-pulse > svg {
        height: 50px !important;
        padding: 5px;
        margin-top: auto;
        margin-bottom: auto;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content {
        margin-left: 70px !important;
        text-align: inherit !important;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .bubble-time {
        flex: 0 1 100%;
        margin-top: 4px;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .bubble-description > span {
        vertical-align: middle;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .bubble-description {
        display: inline-block;
        font-size: 14px !important;
        margin-bottom: 0;
        line-height: inherit !important;
        margin-top: 0 !important;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .ps-bubble-title > span {
        vertical-align: sub;
        font-weight: bolder !important;
        display: inline-block !important;
        line-height: inherit !important;
        padding: 2px 8px !important;
        border-radius: 4px !important;
        min-width: 0 !important;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .ps-bubble-title {
        margin-right: 6px;
        line-height: inherit !important;
        font-size: 15px !important;
        margin-top: 0 !important;
        display: inline-block !important;

    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content {
        min-height: 60px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        text-align: unset !important;
    }

    /*RTL LIVE*/
    #provesrc-notification-container .bubble-body.live-type.rtl-bubble > .bubble-content > .ps-bubble-title {
        margin-left: 0 !important;
        margin-right: 5px !important;
    }

    #provesrc-notification-container .bubble-body.live-type.rtl-bubble > .bubble-content {
        margin-left: 0 !important;
        margin-right: 70px !important;
    }
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .limit-content > strong {
    color: #f03e45;
    padding: 2px 5px;
    background: #ffefef;
    font-size: 1rem;
    font-weight: 700;
    border-radius: 6px;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .box-title {
    padding: 6px 7px 6px 6px;
    background: #ffffff;
    color: #6e6e6e;
    border-radius: 3px;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 0;
    display: inline-block;
    text-align: left;
    width: auto;
    border: 1px solid #d7d7d7;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .limit-content {
    font-size: .9rem;
    color: #767676;
    margin-top: 20px;
    text-align: left;
    line-height: 1.4;
    font-weight: 500;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .bubble-time {
    text-align: center;
    padding: 7px 0;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .limit-title {
    color: #151515;
    margin-left: auto;
    margin-right: auto;
    text-align: left;
    font-size: 1.3rem;
    margin-top: 15px;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached {
    font-size: .9rem;
    text-align: center;
    width: 485px;
    padding: 1.9rem;
    z-index: 2147483010 !important;
}

/*REMARKETING*/
#provesrc-remarketing-container {
    position: absolute;
    width: 300px;
    border-radius: 3px;
    background: #fff;
    text-align: center;
    left: 0;
    bottom: 5px;
    border: 3px solid #7625f3;
    padding: 5px;
    min-height: 100px;
    cursor: pointer;
}

#provesrc-bubble-body-container.plan-limit-reached .lock-icon,
#provesrc-debug-mode .lock-icon,
#provesrc-remarketing-container .lock-icon {
    background-image: url('data:image/svg+xml;base64,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');
    background-size: 14px;
    display: inline-block;
    width: 18px;
    opacity: 0.3;
    height: 14px;
    background-repeat: no-repeat;
    margin-bottom: -1px;
}

#provesrc-bubble-body-container.plan-limit-reached .money-back {
    color: #03ab33 !important;
    padding-bottom: 5px;
    font-weight: bold;
}

#provesrc-bubble-body-container .limit-cta {
    margin-top: 10px;
}

#provesrc-bubble-body-container .limit-cta > a.box-cta {
    display: block;
    padding: 7px 0;
    background: #03ab33;
    color: #fff;
    border: 2px solid #03ab33;
    border-radius: 5px;
    width: 100px;
    font-size: .9rem;
    text-align: center;
    margin-right: auto;
    font-weight: 700;
}

#provesrc-remarketing-container a.box-cta {
    display: block;
    padding: 6px;
    background: #7625f3;
    color: white;
    border: 1px solid #6d1fe7;
    border-radius: 3px;
}

#provesrc-remarketing-container .box-content {
    padding: 15px;
    margin-top: 0px;
    font-size: 14px;
    line-height: 19px !important;
}

#provesrc-bubble-body-container .box-title {
    padding: 5px;
    background: #f7f7f7;
    color: #9f9f9f;
    border-radius: 3px;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    margin-bottom: 8px;
    text-align: center;
}

#provesrc-remarketing-container .box-title {
    padding: 5px;
    background: #f7f7f7;
    color: #9f9f9f;
    border-radius: 3px;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    width: 60%;
    text-align: center;
}

/*LIVE HITS - RTL*/
#provesrc-notification-container .bubble-body.live-type.rtl-bubble > .bubble-content > .ps-bubble-title {
    margin-left: 5px;
    margin-right: 0;
}

#provesrc-notification-container .bubble-body.live-type.rtl-bubble > .bubble-content {
    margin-left: 0;
    margin-right: 67px;
}

/*LIVE HITS*/
#provesrc-notification-container .bubble-body.live-type .bubble-content {
    margin-left: 70px;
}

#provesrc-notification-container .bubble-body.live-type {
    background-color: #ffffff;
    width: 290px;
}

#provesrc-notification-container .bubble-body.live-type .bubble-content > .ps-bubble-title > span {
    font-weight: bolder;
    display: inline-block;
    border-radius: 4px;
    background-color: #F2E9FF;
    color: #7825F3;
}

#provesrc-notification-container .bubble-body.live-type .bubble-content .bubble-time {
    margin-top: 5px;
    margin-bottom: 2px;
}

#provesrc-notification-container .bubble-body.live-type .bubble-content > .ps-bubble-title {
    font-size: 14px;
    display: inline;
    margin-right: 5px;
}

#provesrc-notification-container .bubble-body.live-type svg.ps-pulse-dot > circle {
    stroke: #24a03c;
    fill: #24a03c !important;
    stroke-width: 1px;
    stroke-opacity: 1;
}

#provesrc-notification-container .bubble-body.live-type svg.ps-pulse-dot {
    position: inherit;
    width: 100%;
    height: 100%;
    display: block;
}

#provesrc-notification-container .bubble-body.live-type svg.ps-pulse-dot ._ps-pulse {
    fill: white !important;
    fill-opacity: 0;
    transform-origin: 50% 50%;
    animation-duration: 2s;
    animation-name: _ps-pulse;
    animation-iteration-count: infinite;
}

#provesrc-notification-container .bubble-body.live-type > .bubble-content > .bubble-description {
    font-size: 14px;
    display: inline-block;
}

@keyframes _ps-pulse {
    from {
        stroke-width: 3px;
        stroke-opacity: 1;
        transform: scale(0.3);
    }
    to {
        stroke-width: 0;
        stroke-opacity: 0;
        transform: scale(2);
    }
}


/*INFORMATIONAL*/
#provesrc-notification-container .bubble-body.informational-type .bubble-content .ps-bubble-title span {
    padding: 3px 0;
    background-color: transparent !important;
    border-radius: 0;
    color: #2d2d2d;
}

#provesrc-notification-container .bubble-body.informational-type .bubble-content .bubble-description {
    font-size: 12px !important;
    color: #5e5e5e !important;
    margin-bottom: 4px;
    margin-top: 2px;
    padding-right: 10px;
}

#provesrc-notification-container .bubble-body.informational-type {
    min-height: 60px;
    display: flex;
    align-items: center;
}

#provesrc-notification-container .bubble-body.informational-type.rtl-bubble .bubble-content .bubble-description {
    padding-right: 0;
    padding-left: 10px;
}

#provesrc-notification-container .bubble-body.informational-type.rtl-bubble .bubble-time .ps-link {
    direction: ltr !important;
}

#provesrc-notification-container .bubble-body.informational-type .bubble-time .ps-link {
    margin-left: -2px;
}

/*REVIEW*/

/*RTL*/

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .review-source-container {
    margin-right: 0;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-description > span {
    padding-right: 0;
    padding-left: 5px;
}

#provesrc-debug-mode *,
#provesrc-notification-container * {
    box-sizing: border-box;
    line-height: 17px;
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100% !important;
    vertical-align: baseline;
    letter-spacing: 0px !important;
}

#provesrc-debug-mode .provesrc-logo > img {
    margin-left: auto;
    margin-right: auto;
}

#provesrc-notification-container .pfs-link svg {
    display: inline-block !important;
    width: 13px !important;
    height: 13px !important;
    margin-bottom: -2px;
}

#provesrc-debug-mode .debug-mode-container span.green-dot {
    display: inline-block;
    width: 9px;
    margin-right: 5px;
    height: 9px;
    background: #42ac05;
    border-radius: 50%;
}

#provesrc-debug-mode .debug-mode-container img {
    width: 35px;
    text-align: center;
}

#provesrc-debug-mode .debug-mode-container {
    text-align: center;
}

#provesrc-debug-mode .installed-note {
    margin-bottom: 2px;
    font-size: .9rem;
    color: #7205f7 !important;
}

#provesrc-debug-mode span.admin-note {
    color: #838383;
    font-weight: 400;
}

#provesrc-debug-mode {
    background-color: #fff;
    bottom: 46%;
    color: #7205f7 !important;
    left: -4px;
    font-size: .8rem;
    font-weight: bold;
    padding: 8px;
    border: 3px solid #7225f3;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

#provesrc-debug-mode,
#provesrc-widget-area {
    z-index: 2147483647;
}

#provesrc-debug-mode,
#provesrc-notification-container {
    position: fixed !important;
    font-family: Lato, arial, sans-serif !important;
}

/*COMBO RTL NOTIFICATION*/
#provesrc-notification-container .bubble-body.rtl-bubble.combo-type .bubble-icon {
    padding-right: 0 !important;
    padding-left: 5px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.combo-type .bubble-content {
    margin-right: auto;
    margin-left: 0;
    padding-left: 21px;
    padding-right: 0;
    font-family: Lato, arial, sans-serif !important;
}

/*RTL*/
#provesrc-notification-container .bubble-body.rtl-bubble {
    direction: rtl !important;
    text-align: right !important;
    font-family: Arial, sans-serif;
}

#provesrc-notification-container .bubble-body.rtl-bubble .bubble-content .bubble-cta .cta-arrow::after {
    border-right: 0;
    border-top: 0;
    border-left: 0.1em solid;
    border-bottom: 0.1em solid;
    margin-left: 0;
    margin-right: 0.3em;
}

#provesrc-notification-container .bubble-body.rtl-bubble .bubble-icon {
    border-right: 0;
    /*border-left: 1px solid #ebebeb;*/
}

#provesrc-notification-container .bubble-body.rtl-bubble #ps-bubble-close {
    left: 3px !important;
    right: inherit !important;
}

#provesrc-notification-container .bubble-body.rtl-bubble .bubble-content {
    display: flex;
    min-width: 0;
    margin-right: 0;
    padding: 7px;
    font-family: Lato, arial, sans-serif !important;
}

/*LTR*/

#provesrc-notification-container.bottom-center .bubble-body,
#provesrc-notification-container.top-left .bubble-body,
#provesrc-notification-container.bottom-left .bubble-body {
    float: left;
}

#provesrc-notification-container.top-right .bubble-body,
#provesrc-notification-container.bottom-right .bubble-body {
    float: right;
}

#provesrc-notification-container.bottom-center {
    left: 50%;
    bottom: 10px;
    margin-left: -165px;
}

#provesrc-notification-container.bottom-left {
    left: 10px;
    bottom: 10px;
}

#provesrc-notification-container.bottom-right {
    right: 10px;
    bottom: 10px;
}

#provesrc-notification-container.top-right {
    right: 10px;
    top: 10px;
}

#provesrc-notification-container.top-left {
    left: 10px;
    top: 10px;
}

#provesrc-notification-container .bubble-body:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, .2);
    -webkit-box-shadow: 0 6px 20px rgba(0, 0, 0, .2);
    -moz-box-shadow: 0 6px 20px rgba(0, 0, 0, .2);
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
    top: -10px;
}

#provesrc-notification-container .bubble-body {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer !important;
    direction: ltr !important;
    text-align: left !important;
    width: 330px;
    overflow: hidden;
    border-radius: 6px;
    background-color: #FFF;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15) !important;
    -webkit-box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15) !important;
    -moz-box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #ececec;
    font-family: Lato, arial, sans-serif !important;
    transition: all .3s;
    top: 0;
}

#provesrc-notification-container .bubble-content .bubble-cta .cta-arrow::after {
    position: relative;
    content: '';
    display: inline-block;
    width: 0.4em;
    height: 0.4em;
    border-right: 0.1em solid;
    border-top: 0.1em solid;
    transform: rotate(45deg) !important;
    margin-left: 0.3em;
    font-size: 17px;
    -webkit-animation: provesrc-arrow-bouncer 2s infinite;
    animation: provesrc-arrow-bouncer 2s infinite;
}

#provesrc-notification-container .bubble-content .bubble-cta {
    line-height: 17px;
    font-size: 12px !important;
    color: #a0a0a0;
    padding: 3px 0;
    border-radius: 5px;
    margin-bottom: 5px;
    margin-top: 6px;
    display: inline-block;
}


#provesrc-notification-container .bubble-body .bubble-content {
    min-width: 0;
    margin-left: 0;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body.stream-type .bubble-content {
    display: flex;
    padding: 7px;
    align-items: center;
}

#provesrc-notification-container .bubble-body.review-type .bubble-content {
    flex-direction: column;
    display: flex;
    padding: 7px;
    margin-left: 12px;
}

#provesrc-notification-container .bubble-body.review-type .bubble-icon img {
    max-width: 55px;
    max-height: 60px;
}

#provesrc-notification-container .bubble-body .bubble-content-inner {
    flex: 1 1 0%;
    min-width: 0;
    margin: auto;
}

#provesrc-notification-container .bubble-body .bubble-content .bubble-time a,
#provesrc-notification-container .bubble-body .bubble-content .bubble-time span {
    display: inline !important;
    font-size: 11px !important;
    font-family: Lato, arial, sans-serif !important;
}


#provesrc-notification-container .bubble-body .bubble-content .bubble-time {
    display: flex;
    gap: 5px;
    font-size: 11px !important;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body .bubble-content .bubble-description > span {
    font-size: 12px !important;
}

#provesrc-notification-container .bubble-body .bubble-content .bubble-description {
    line-height: 17px;
    font-size: 12px !important;
    margin-bottom: 2px;
    margin-top: 2px;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body .bubble-content .ps-bubble-title span {
    font-weight: 700;
    display: inline-block;
    padding: 3px 5px;
    border-radius: 3px;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body .bubble-content .ps-bubble-title {
    line-height: 17px;
    font-size: 15px !important;
    font-weight: bold;
    font-family: Lato, arial, sans-serif !important;
    margin-bottom: 2px;
}

#provesrc-notification-container .bubble-body .bubble-icon {
    min-width: 50px;
    min-height: 55px;
    display: flex;
    position: relative;
}

#provesrc-notification-container .bubble-body .bubble-icon svg {
    flex: 0 0 auto;
}

#provesrc-notification-container .bubble-body .bubble-time .pfs-link a {
    text-decoration: none;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .bubble-body .bubble-time .pfs-link span.bubble-bolt > i {
    display: inline-block !important;
}

#provesrc-notification-container .bubble-body .bubble-time .pfs-link {
    display: inline-block;
    font-size: 11px !important;
    font-family: Lato, arial, sans-serif !important;
}

#provesrc-notification-container .text-capitalize {
    text-transform: capitalize;
}

#provesrc-notification-container .text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

#provesrc-notification-container .no-text-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#provesrc-notification-container .mx-auto {
    margin-right: auto !important;
    margin-left: auto !important;
}

#provesrc-notification-container.ps-show {
    display: block;
}

#provesrc-notification-container.ps-hide {
    display: none;
}

#provesrc-notification-container #ps-bubble-close:hover {
    opacity: 0.8;
}

#provesrc-notification-container #ps-bubble-close {
    position: absolute;
    top: 3px;
    right: 3px;
    height: 13px;
    width: 13px;
    transform: rotate(45deg);
    cursor: pointer;
    opacity: 0.5;
    zoom: 1.3;
}

/*#provesrc-notification-container #ps-bubble-close:before {*/
/*content: '';*/
/*display: block;*/
/*width: 100%;*/
/*height: 3px;*/
/*background-color: #c4c4c4;*/
/*position: absolute;*/
/*left: 0;*/
/*top: 5px;*/
/*}*/

/*#provesrc-notification-container #ps-bubble-close:after {*/
/*content: '';*/
/*display: block;*/
/*height: 100%;*/
/*width: 3px;*/
/*background-color: #c4c4c4;*/
/*position: absolute;*/
/*left: 5px;*/
/*top: 0;*/
/*}*/

#provesrc-widget-area .ps-animated {
    -webkit-animation-duration: .5s;
    animation-duration: .5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

#provesrc-notification-container .no-visible {
    visibility: hidden;
}

#provesrc-notification-container .is-visible {
    visibility: visible;
}

/*STREAM NOTIFICATION*/
#provesrc-notification-container .bubble-body.stream-type a#ps-stream-product-link:hover {
    -webkit-text-decoration: underline !important;
    -moz-text-decoration: underline !important;
    -ms-text-decoration: underline !important;
    -o-text-decoration: underline !important;
    text-decoration: underline !important;
    cursor: pointer !important;
}

#provesrc-notification-container .bubble-body.stream-type a#ps-stream-product-link {
    font-weight: bold !important;
    color: #454545;
    -webkit-text-decoration: none;
    -moz-text-decoration: none;
    -ms-text-decoration: none;
    -o-text-decoration: none;
    text-decoration: none;
}

#provesrc-notification-container .bubble-body.stream-type a#ps-stream-no-product-link {
    font-weight: bold !important;
    color: #454545;
    -webkit-text-decoration: none;
    -moz-text-decoration: none;
    -ms-text-decoration: none;
    -o-text-decoration: none;
    text-decoration: none;
}


#provesrc-notification-container .bubble-body.stream-type .bubble-icon {
    border-right: none;
}

#provesrc-notification-container .bubble-body.stream-type span.stream-location > img.location-flag {
    max-width: 17px !important;
    width: 17px !important;
    border-radius: 2px !important;
    vertical-align: text-top !important;
    border: 1px solid #d2d2d2 !important;
    margin-left: 4px !important;
}

#provesrc-notification-container .bubble-body.stream-type .stream-location {
    font-size: 12px !important;
    opacity: 0.6 !important;
    font-weight: normal !important;
    color: inherit !important;
}

#provesrc-notification-container .bubble-body.review-type .bubble-icon span.bubble-initials,
#provesrc-notification-container .bubble-body.stream-type .bubble-icon span.bubble-initials {
    font-weight: bolder;
    font-size: 27px !important;
    align-content: center;
    height: 55px
}

#provesrc-notification-container .bubble-body.stream-type .bubble-icon img {
    max-width: 65px;
    max-height: 55px;
}

#provesrc-notification-container .bubble-body.stream-type .bubble-content .ps-bubble-title span {
    background-color: transparent;
    padding: 0 !important;
    max-width: 160px;
    vertical-align: middle;
}

#provesrc-notification-container .bubble-body.stream-type .bubble-content .bubble-description {
    margin-top: 0;
}

/*STREAM RTL*/
#provesrc-notification-container .bubble-body.stream-type.rtl-bubble {
    border-radius: inherit;
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

#provesrc-notification-container .bubble-body.stream-type.rtl-bubble .bubble-icon img {
    right: 2px;
}

#provesrc-notification-container .bubble-body.stream-type.rtl-bubble .bubble-icon {
    border-right: 0;
    border-left: none;
}

#provesrc-notification-container .bubble-body.review-type.rtl-bubble .bubble-icon span.bubble-initials,
#provesrc-notification-container .bubble-body.stream-type.rtl-bubble .bubble-icon span.bubble-initials {
    margin-right: 1px;
}

/*COMBO NOTIFICATION*/
#provesrc-notification-container .bubble-body.combo-type {
    background-color: #ffffff;
    width: 350px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.combo-type .bubble-icon {
    border-left: 0;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-icon {
    border-right: 0;
    width: 35%;
    padding-right: 5px;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-time {
    color: #7627f3;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-time .pfs-link a {
    color: #7627f3;
    text-decoration: none;
    font-weight: 700;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-time .pfs-link {
    padding: 1px 5px;
    background-color: #fff;
    border-radius: 2px;
    margin-top: 5px;
    border: 1px solid #7627f3;
    color: #7627f3;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-content.no-branding {
    padding-top: 20px;
    padding-bottom: 20px;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-content .bubble-description {
    color: #7627f3;
    font-size: 12px;
}

#provesrc-notification-container #ps-bubble-close > span.close-before {
    content: '';
    display: block;
    width: 100%;
    height: 3px;
    background-color: #c4c4c4;
    position: absolute;
    left: 0;
    top: 5px;
}

#provesrc-notification-container #ps-bubble-close > span.close-after {
    content: '';
    display: block;
    height: 100%;
    width: 3px;
    background-color: #c4c4c4;
    position: absolute;
    left: 5px;
    top: 0;
}

#provesrc-notification-container .bubble-body.combo-type #ps-bubble-close > span.close-after,
#provesrc-notification-container .bubble-body.combo-type #ps-bubble-close > span.close-before {
    background-color: #7627f3;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-content {
    margin-left: auto;
    padding-top: 10px;
    padding-right: 21px;
    padding-bottom: 10px;
    width: 65%;
    margin-top: 5px;
    margin-bottom: 5px;
}

#provesrc-notification-container .bubble-body.combo-type .bubble-icon .combo-number > .refer {
    /*margin-top: -7px;*/
    font-weight: normal !important;
    font-size: 12px !important;
    /*font-size: 1vw;*/
}

#provesrc-notification-container .bubble-body.combo-type .bubble-icon .combo-number > .num-value {
    font-size: 28px !important;
    /*font-size: 2vw;*/
    font-weight: 900 !important;
    /*margin-top: -5px;*/
    line-height: 30px !important;
}

#provesrc-notification-container .scale {
    animation: pound 1s;
    animation-delay: 1s;
    animation-iteration-count: 3;
}

@keyframes pound {
    from {
        transform: none;
    }
    50% {
        transform: scale(1.2);
    }
    to {
        transform: none;
    }
}

.provesrc-arrow-bouncer {
    -webkit-animation: provesrc-arrow-bouncer 2s infinite;
    animation: provesrc-arrow-bouncer 2s infinite;
}

@-webkit-keyframes provesrc-arrow-bouncer {
    0%,
    20%,
    50%,
    80%,
    100% {
        -webkit-transform: translateX(0) rotate(45deg);
        transform: translateX(0) rotate(45deg);
    }
    40% {
        -webkit-transform: translateX(-5px) rotate(45deg);
        transform: translateX(-5px) rotate(45deg);
    }
    60% {
        -webkit-transform: translateX(-3px) rotate(45deg);
        transform: translateX(-3px) rotate(45deg);
    }
}

@keyframes provesrc-arrow-bouncer {
    0%,
    20%,
    50%,
    80%,
    100% {
        -webkit-transform: translateX(0) rotate(45deg);
        transform: translateX(0) rotate(45deg);
    }
    40% {
        -webkit-transform: translateX(-5px) rotate(45deg);
        transform: translateX(-5px) rotate(45deg);
    }
    60% {
        -webkit-transform: translateX(-3px) rotate(45deg);
        transform: translateX(-3px) rotate(45deg);
    }
}

#provesrc-notification-container .bubble-body.combo-type .bubble-icon .combo-number {
    position: relative;
    float: left;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #7627f3;
    width: 100%;
    text-align: center;
}

@-webkit-keyframes psFadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes psFadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.psFadeInUp {
    -webkit-animation-name: psFadeInUp;
    animation-name: psFadeInUp;
}

@-webkit-keyframes fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes psBounceInUp {
    from {
        opacity: 0.5;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.psBounceInUp {
    -webkit-animation-name: psBounceInUp;
    animation-name: psBounceInUp;
}

@-webkit-keyframes psBounceInDown {
    from {
        opacity: 0.5;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@keyframes psBounceInDown {
    from {
        opacity: 0.5;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

.psBounceInDown {
    -webkit-animation-name: psBounceInDown;
    animation-name: psBounceInDown;
}

@-webkit-keyframes psFadeOutDown {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@keyframes psFadeOutDown {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

.psFadeOutDown {
    -webkit-animation-name: psFadeOutDown;
    animation-name: psFadeOutDown;
}

@-webkit-keyframes psFadeOutUp {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@keyframes psFadeOutUp {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

.psFadeOutUp {
    -webkit-animation-name: psFadeOutUp;
    animation-name: psFadeOutUp;
}

/*MOBILE WEB*/
@media only screen and (max-width: 815px) {
    #provesrc-notification-container {
        bottom: 0 !important;
        left: 0 !important;
        width: 100%;
        max-width: 100%;
    }

    #provesrc-notification-container._ps-mobile-small > .bubble-body {
        width: 100%;
        max-width: 75% !important;
        zoom: 0.8;
        border-radius: 0;
    }

    #provesrc-notification-container._ps-mobile-small > .review-type ._ps-review-rating > .review-source-container {
        margin-right: 2px !important;
    }

    #provesrc-notification-container._ps-mobile-small ._ps-review-rating > .review-source-container > span.review-source-name {
        display: none !important;
    }

    #provesrc-notification-container .bubble-body:hover {
        box-shadow: none;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        -webkit-transition: none;
        -moz-transition: none;
        -ms-transition: none;
        -o-transition: none;
        transition: none;
        top: 0;
    }

    #provesrc-notification-container .bubble-body.live-type,
    #provesrc-notification-container .bubble-body.combo-type {
        width: 100% !important;
    }

    #provesrc-notification-container > .bubble-body {
        width: 100%;
        max-width: 100%;
        border-radius: 0;
    }

    #provesrc-notification-container .bubble-content .bubble-cta {
        margin-bottom: 0;
        margin-top: 0;
    }

    #provesrc-notification-container.top-left,
    #provesrc-notification-container.top-right {
        right: 0 !important;
        top: 0 !important;
        height: 50px;
    }

    #provesrc-notification-container.bottom-center {
        margin-left: 0;
    }

    #provesrc-notification-container.bottom-center,
    #provesrc-notification-container.bottom-left,
    #provesrc-notification-container.bottom-right {
        right: 0 !important;
        bottom: 0 !important;
    }

    #provesrc-notification-container #ps-bubble-close span.close-after {
        height: 13px !important;
    }

    #provesrc-notification-container #ps-bubble-close span.close-before {
        width: 13px !important;
    }

    #provesrc-notification-container #ps-bubble-close {
        padding: 15px !important;
        top: 11px !important;
        right: -1px !important;
    }

    /*LIVE*/
    #provesrc-notification-container > .bubble-body.live-type > .bubble-live-pulse {
        float: left;
        width: 67px;
        display: block;
        position: absolute;
        border-right: none;
        height: 100%;
        margin-top: 0 !important;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-live-pulse > svg {
        height: 50px !important;
        padding: 5px;
        margin-top: auto;
        margin-bottom: auto;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content {
        margin-left: 70px !important;
        text-align: inherit !important;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .bubble-time {
        flex: 0 1 100%;
        margin-top: 4px;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .bubble-description > span {
        vertical-align: middle;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .bubble-description {
        display: inline-block;
        font-size: 14px !important;
        margin-bottom: 0;
        line-height: inherit !important;
        margin-top: 0 !important;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .ps-bubble-title > span {
        vertical-align: sub;
        font-weight: bolder !important;
        display: inline-block !important;
        line-height: inherit !important;
        padding: 2px 8px !important;
        border-radius: 4px !important;
        min-width: 0 !important;
    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content > .ps-bubble-title {
        margin-right: 6px;
        line-height: inherit !important;
        font-size: 15px !important;
        margin-top: 0 !important;
        display: inline-block !important;

    }

    #provesrc-notification-container > .bubble-body.live-type > .bubble-content {
        min-height: 60px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        text-align: unset !important;
    }

    /*RTL LIVE*/
    #provesrc-notification-container .bubble-body.live-type.rtl-bubble > .bubble-content > .ps-bubble-title {
        margin-left: 0 !important;
        margin-right: 5px !important;
    }

    #provesrc-notification-container .bubble-body.live-type.rtl-bubble > .bubble-content {
        margin-left: 0 !important;
        margin-right: 70px !important;
    }
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .limit-content > strong {
    color: #f03e45;
    padding: 2px 5px;
    background: #ffefef;
    font-size: 1rem;
    font-weight: 700;
    border-radius: 6px;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .box-title {
    padding: 6px 7px 6px 6px;
    background: #ffffff;
    color: #6e6e6e;
    border-radius: 3px;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 0;
    display: inline-block;
    text-align: left;
    width: auto;
    border: 1px solid #d7d7d7;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .limit-content {
    font-size: .9rem;
    color: #767676;
    margin-top: 20px;
    text-align: left;
    line-height: 1.4;
    font-weight: 500;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .bubble-time {
    text-align: center;
    padding: 7px 0;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached > .limit-title {
    color: #151515;
    margin-left: auto;
    margin-right: auto;
    text-align: left;
    font-size: 1.3rem;
    margin-top: 15px;
}

#provesrc-bubble-body-container.bubble-body.plan-limit-reached {
    font-size: .9rem;
    text-align: center;
    width: 485px;
    padding: 1.9rem;
    z-index: 2147483010 !important;
}

/*REMARKETING*/
#provesrc-remarketing-container {
    position: absolute;
    width: 300px;
    border-radius: 3px;
    background: #fff;
    text-align: center;
    left: 0;
    bottom: 5px;
    border: 3px solid #7625f3;
    padding: 5px;
    min-height: 100px;
    cursor: pointer;
}

#provesrc-bubble-body-container.plan-limit-reached .lock-icon,
#provesrc-debug-mode .lock-icon,
#provesrc-remarketing-container .lock-icon {
    background-image: url('data:image/svg+xml;base64,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');
    background-size: 14px;
    display: inline-block;
    width: 18px;
    opacity: 0.3;
    height: 14px;
    background-repeat: no-repeat;
    margin-bottom: -1px;
}

#provesrc-bubble-body-container.plan-limit-reached .money-back {
    color: #03ab33 !important;
    padding-bottom: 5px;
    font-weight: bold;
}

#provesrc-bubble-body-container .limit-cta {
    margin-top: 10px;
}

#provesrc-bubble-body-container .limit-cta > a.box-cta {
    display: block;
    padding: 7px 0;
    background: #03ab33;
    color: #fff;
    border: 2px solid #03ab33;
    border-radius: 5px;
    width: 100px;
    font-size: .9rem;
    text-align: center;
    margin-right: auto;
    font-weight: 700;
}

#provesrc-remarketing-container a.box-cta {
    display: block;
    padding: 6px;
    background: #7625f3;
    color: white;
    border: 1px solid #6d1fe7;
    border-radius: 3px;
}

#provesrc-remarketing-container .box-content {
    padding: 15px;
    margin-top: 0px;
    font-size: 14px;
    line-height: 19px !important;
}

#provesrc-bubble-body-container .box-title {
    padding: 5px;
    background: #f7f7f7;
    color: #9f9f9f;
    border-radius: 3px;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    margin-bottom: 8px;
    text-align: center;
}

#provesrc-remarketing-container .box-title {
    padding: 5px;
    background: #f7f7f7;
    color: #9f9f9f;
    border-radius: 3px;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    width: 60%;
    text-align: center;
}

/*LIVE HITS - RTL*/
#provesrc-notification-container .bubble-body.live-type.rtl-bubble > .bubble-content > .ps-bubble-title {
    margin-left: 5px;
    margin-right: 0;
}

#provesrc-notification-container .bubble-body.live-type.rtl-bubble > .bubble-content {
    margin-left: 0;
    margin-right: 67px;
}

/*LIVE HITS*/
#provesrc-notification-container .bubble-body.live-type .bubble-content {
    margin-left: 70px;
}

#provesrc-notification-container .bubble-body.live-type {
    background-color: #ffffff;
    width: 290px;
}

#provesrc-notification-container .bubble-body.live-type .bubble-content > .ps-bubble-title > span {
    font-weight: bolder;
    display: inline-block;
    border-radius: 4px;
    background-color: #F2E9FF;
    color: #7825F3;
}

#provesrc-notification-container .bubble-body.live-type .bubble-content .bubble-time {
    margin-top: 5px;
    margin-bottom: 2px;
}

#provesrc-notification-container .bubble-body.live-type .bubble-content > .ps-bubble-title {
    font-size: 14px;
    display: inline;
    margin-right: 5px;
}

#provesrc-notification-container .bubble-body.live-type svg.ps-pulse-dot > circle {
    stroke: #24a03c;
    fill: #24a03c !important;
    stroke-width: 1px;
    stroke-opacity: 1;
}

#provesrc-notification-container .bubble-body.live-type svg.ps-pulse-dot {
    position: inherit;
    width: 100%;
    height: 100%;
    display: block;
}

#provesrc-notification-container .bubble-body.live-type svg.ps-pulse-dot ._ps-pulse {
    fill: white !important;
    fill-opacity: 0;
    transform-origin: 50% 50%;
    animation-duration: 2s;
    animation-name: _ps-pulse;
    animation-iteration-count: infinite;
}

#provesrc-notification-container .bubble-body.live-type > .bubble-content > .bubble-description {
    font-size: 14px;
    display: inline-block;
}

@keyframes _ps-pulse {
    from {
        stroke-width: 3px;
        stroke-opacity: 1;
        transform: scale(0.3);
    }
    to {
        stroke-width: 0;
        stroke-opacity: 0;
        transform: scale(2);
    }
}


/*INFORMATIONAL*/
#provesrc-notification-container .bubble-body.informational-type .bubble-content .ps-bubble-title span {
    padding: 3px 0;
    background-color: transparent !important;
    border-radius: 0;
    color: #2d2d2d;
}

#provesrc-notification-container .bubble-body.informational-type .bubble-content .bubble-description {
    font-size: 12px !important;
    color: #5e5e5e !important;
    margin-bottom: 4px;
    margin-top: 2px;
    padding-right: 10px;
}

#provesrc-notification-container .bubble-body.informational-type {
    min-height: 60px;
    display: flex;
    align-items: center;
}

#provesrc-notification-container .bubble-body.informational-type.rtl-bubble .bubble-content .bubble-description {
    padding-right: 0;
    padding-left: 10px;
}

#provesrc-notification-container .bubble-body.informational-type.rtl-bubble .bubble-time .ps-link {
    direction: ltr !important;
}

#provesrc-notification-container .bubble-body.informational-type .bubble-time .ps-link {
    margin-left: -2px;
}

/*REVIEW*/

/*RTL*/

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .review-source-container {
    margin-right: 0;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-description > span {
    padding-right: 0;
    padding-left: 5px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-icon {
    padding-right: 7px;
    padding-left: 0;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-content {
    margin-left: 0 !important;
    margin-right: 7px !important;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type ._ps-review-rating {
    padding-right: 0;
    padding-left: 5px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .review-source-container .review-source-name {
    display: none;
    margin-left: 5px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type ._ps-review-rating img._ps-review-source {
    margin-right: 0;
    margin-left: 5px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-time .pfs-link {
    float: left;
    margin-left: 20px;
}

/*LTR*/
#provesrc-notification-container .bubble-body.review-type .bubble-time .pfs-link {
    float: right;
    margin-right: 20px;
}

#provesrc-notification-container .bubble-body.review-type .bubble-icon {
    padding-right: 0;
    padding-left: 7px;
}

#provesrc-notification-container .bubble-body.review-type .bubble-description > span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-height: 16px;
    margin-bottom: 8px;
    padding-right: 5px;
    max-height: 50px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    -o-text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    font-style: italic;
    font-size: 12px;
}

#provesrc-notification-container .bubble-body.review-type .ps-bubble-title > span {
    padding: 3px 0;
}

#provesrc-notification-container .bubble-body.review-type ._ps-review-rating img._ps-review-source {
    width: 11px;
    margin-right: 4px;
    display: inline-block;
    vertical-align: text-bottom;
}

#provesrc-notification-container .bubble-body.review-type ._ps-review-rating {
    display: inline-block;
    padding-right: 5px;
}

#provesrc-notification-container .bubble-body.review-type .review-source-container {
    display: inline-block;
    margin-right: 8px;
}

#provesrc-notification-container svg {
    position: unset;
    top: unset;
    left: unset;
}

.map-container {
    position: relative;
    display: inline-block;
    width: 100px;  /* Adjust to your map image size */
    height: 100px; /* Adjust to your map image size */
}
.map-container img {
    width: 100%;
    height: 100%;
    display: block;
}
.map-overlay {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.4); /* Adjust opacity for darkness */
    pointer-events: none;
    border-radius: 8px; /* Match your image's border radius if needed */
}

#provesrc-notification-container .bubble-body.review-type ._ps-review-rating ._ps-review-rating-star svg {
    display: inline !important;
    color: #ffc100 !important;
    fill: rgb(255, 193, 0);
}

#provesrc-notification-container .bubble-body.review-type ._ps-review-rating ._ps-review-rating-star {
    color: #ffc100;
    font-size: 10px !important;
    vertical-align: middle;
}

#provesrc-notification-container .bubble-body.social-type {
    display: flex;
    justify-content: center;
    flex-direction: column;
}

#provesrc-notification-container .bubble-body.social-type .social-text {
    font-size: 14px;
    margin: 9px 13px 0px 13px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-wrap: normal;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    -o-text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    line-height: 17px;
    max-height: 35px;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description .social-item img {
    width: 40px;
    display: block;
    margin: 0px auto;
    margin-bottom: 2px;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description .social-item span {
    padding-top: 1px;
    display: block;
    margin: 0px auto;
    text-align: center;
    font-weight: 700;
    margin-bottom: 5px;
    font-size: 12px !important;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 1.5rem;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 1.5rem;
    width: 100%;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description .social-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description.two-icons,
#provesrc-notification-container .bubble-body.social-type .bubble-description.three-icons,
#provesrc-notification-container .bubble-body.social-type .bubble-description.four-icons {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

#provesrc-notification-container img{
    opacity: 1 !important;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-icon {
    padding-right: 7px;
    padding-left: 0;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-content {
    margin-left: 0 !important;
    margin-right: 12px !important;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type ._ps-review-rating {
    padding-right: 0;
    padding-left: 5px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .review-source-container .review-source-name {
    display: none;
    margin-left: 5px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type ._ps-review-rating img._ps-review-source {
    margin-right: 0;
    margin-left: 5px;
}

#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-time .pfs-link {
    float: left;
    margin-left: 20px;
}

/*LTR*/
#provesrc-notification-container .bubble-body.review-type .bubble-time .pfs-link {
    float: right;
    margin-right: 20px;
}

#provesrc-notification-container .bubble-body.review-type .bubble-icon {
    padding-right: 0;
    padding-left: 7px;
}

#provesrc-notification-container .bubble-body.review-type .bubble-icon svg {
    min-width: 55px;
    min-height: 55px;
}


#provesrc-notification-container .bubble-body.review-type .bubble-description > span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-height: 16px;
    margin-bottom: 8px;
    padding-right: 5px;
    max-height: 50px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    -o-text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    font-style: italic;
    font-size: 12px;
}

#provesrc-notification-container .bubble-body.review-type .ps-bubble-title > span {
    padding: 3px 0;
}

#provesrc-notification-container .bubble-body.review-type ._ps-review-rating img._ps-review-source {
    width: 11px;
    margin-right: 4px;
    display: inline-block;
    vertical-align: text-bottom;
}

#provesrc-notification-container .bubble-body.review-type ._ps-review-rating {
    display: inline-block;
    padding-right: 5px;
}

#provesrc-notification-container .bubble-body.review-type .review-source-container {
    display: inline-block;
    margin-right: 8px;
}

#provesrc-notification-container svg {
    position: unset;
    top: unset;
    left: unset;
}

.map-container {
    position: relative;
    display: inline-block;
    width: 100px;  /* Adjust to your map image size */
    height: 100px; /* Adjust to your map image size */
}
.map-container img {
    width: 100%;
    height: 100%;
    display: block;
}
.map-overlay {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.4); /* Adjust opacity for darkness */
    pointer-events: none;
    border-radius: 8px; /* Match your image's border radius if needed */
}

#provesrc-notification-container .bubble-body.review-type ._ps-review-rating ._ps-review-rating-star svg {
    display: inline !important;
    color: #ffc100 !important;
    fill: rgb(255, 193, 0);
}

#provesrc-notification-container .bubble-body.review-type ._ps-review-rating ._ps-review-rating-star {
    color: #ffc100;
    font-size: 10px !important;
    vertical-align: middle;
}

#provesrc-notification-container .bubble-body.social-type {
    display: flex;
    justify-content: center;
    flex-direction: column;
}

#provesrc-notification-container .bubble-body.social-type .social-text {
    font-size: 14px;
    margin: 9px 13px 0px 13px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-wrap: normal;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    -o-text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    line-height: 17px;
    max-height: 35px;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description .social-item img {
    width: 40px;
    display: block;
    margin: 0px auto;
    margin-bottom: 2px;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description .social-item span {
    padding-top: 1px;
    display: block;
    margin: 0px auto;
    text-align: center;
    font-weight: 700;
    margin-bottom: 5px;
    font-size: 12px !important;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 1.5rem;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 1.5rem;
    width: 100%;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description .social-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

#provesrc-notification-container .bubble-body.social-type .bubble-description.two-icons,
#provesrc-notification-container .bubble-body.social-type .bubble-description.three-icons,
#provesrc-notification-container .bubble-body.social-type .bubble-description.four-icons {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

#provesrc-notification-container img{
    opacity: 1 !important;
}
