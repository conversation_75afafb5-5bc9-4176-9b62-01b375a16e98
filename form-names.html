<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>ProveSource Form Name Fields Test</title>
	<style>
		.section {
			padding: 15px;
			border: 3px solid black;
			font-family: sans-serif, Arial;
		}

		.section-title{
			margin-bottom: 15px;
			padding: 5px;
			background-color: #f4f4f4;
		}

		.section h4 {
			padding: 5px;
			background: #dddddd;
			margin: 0;
			font-size: 1.3rem;
		}

		button, input[type=submit] {
			padding: 10px;
			background: #8238f3 !important;
			color: white !important;
			border-radius: 4px;
			cursor: pointer;
			margin: 5px;
			height: 35px !important;
			line-height: 0px !important;

		}

		input {
			padding: 5px !important;
			height: 20px !important;
			margin: 5px !important;
			line-height: 15px;
		}
	</style>

</head>
<body>
<!-- Start of Async ProveSource Code --><script>!function(o,i){window.provesrc&&window.console&&console.error&&console.error("ProveSource is included twice in this page."),provesrc=window.provesrc={dq:[],display:function(){this.dq.push(arguments)}},o._provesrcAsyncInit=function(){provesrc.init({apiKey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************.njkcSFOkHCztWBzf319Nlt_Af2bFbFvYDGtp1UdPlgo",v:"0.0.4"})};var r=i.createElement("script");r.type="text/javascript",r.async=!0,r["ch"+"ar"+"set"]="UTF-8",r.src="provesrc.js";var e=i.getElementsByTagName("script")[0];e.parentNode.insertBefore(r,e)}(window,document);</script><!-- End of Async ProveSource Code -->



<div style="font-size: 1rem; font-family: Arial,sans-serif; font-weight: bold; padding: 15px; text-align: center;font-size: 30px">
	ProveSource Test Page
</div>
<div class="section">
	<div class="section-title">Submit button is outside the form</div>
	<form id="ps-form" name="testForm" method="post">
		<input type="text" name="form_submission[name]" id="form_submission_name" value="" required="required" class="form-control" placeholder="First Name" data-parsley-id="8240">
		<input type="text" name="first-name" value="John"/>
		<input type="text" name="LaST_NAme" value="Doe"/>
		<input type="text" placeholder="first name" value="Doe"/>
		<input type="text" placeholder="whats your last name"/>
		<input id="j_idt208:lastName" name="j_idt208:lastName" type="text" placeholder="Nazwisko" class="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all" role="textbox" aria-disabled="false" aria-readonly="false">
		<input placeholder="Enter Your Name" name="name" class="elInput elInput100 elAlign_left elInputBR5 elInputIRight elInputMid elInputStyle1 elInputBG1 elInputIColor required1 elInputBoldNo elInputIName garlic-auto-save" data-type="extra" type="text">
		<input type="email" name="email" value="<EMAIL>"/>
	</form>
	<button>Go</button>
</div>

<div class="section">
	<div class="section-title">Bookly</div>
	<div id="bookly-form-5b8544401ba22" class="bookly-form" data-form_id="5b8544401ba22" ps-dynamic-form-id="_ps-7o6r3cg8k" style="border: 2px dashed red;"><div class="bookly-progress-tracker bookly-table">
		<div class="active">
			1. Wat?        <div class="step"></div>
		</div>
		<div class="active">
			2. Wanneer?        <div class="step"></div>
		</div>
		<div class="active">
			3. Details        <div class="step"></div>
		</div>
		<div>
			4. Klaar        <div class="step"></div>
		</div>
	</div>
		<div class="bookly-box">Je hebt een reservering voor <b>Pasafspraak (1e keer)</b> door <b>Britt</b> op <b>10:30</b> op <b>30 augustus 2018</b> geselecteerd.<br>
			Vul je gegevens in het formulier hieronder in om door te gaan met reserveren.</div>

		<div class="bookly-details-step">
			<div class="bookly-box bookly-table">
				<div class="bookly-form-group">
					<label>Voornaam</label>
					<div>
						<input class="bookly-js-first-name" type="text" value="">
					</div>
					<div class="bookly-js-first-name-error bookly-label-error"></div>
				</div>
				<div class="bookly-form-group">
					<label>Achternaam</label>
					<div>
						<input class="bookly-js-last-name" type="text" value="">
					</div>
					<div class="bookly-js-last-name-error bookly-label-error"></div>
				</div>
			</div>

			<div class="bookly-box bookly-table">
				<div class="bookly-form-group">
					<label>Telefoon</label>
					<div class="bookly-js-user-phone-error bookly-label-error"></div>
				</div>
				<div class="bookly-form-group">
					<label>E-mail</label>
					<div>
						<input class="bookly-js-user-email" maxlength="255" type="text" value="" ps-email-field-id="_ps-7kch701wq" style="border: 2px dashed red;">
					</div>
					<div class="bookly-js-user-email-error bookly-label-error"></div>
				</div>
			</div>

			<div id="bookly-js-address">

				<div class="bookly-box bookly-bold">
					Adres            </div>

				<div class="bookly-box">
					<div class="bookly-form-group">
						<label>Postcode</label>
						<div>
							<input type="text" class="bookly-js-address-postcode" value="" maxlength="255">
						</div>
						<div class="bookly-js-address-postcode-error bookly-label-error"></div>
					</div>
				</div>
				<div class="bookly-box">
					<div class="bookly-form-group">
						<label>Woonplaats</label>
						<div>
							<input type="text" class="bookly-js-address-city" value="" maxlength="255">
						</div>
						<div class="bookly-js-address-city-error bookly-label-error"></div>
					</div>
				</div>

			</div>


			<div class="bookly-box">
				<div class="bookly-form-group">
					<label>Heb je een favoriete jurk? Of merk? Waneer ga je trouwen :D ? We zijn benieuwd naar jou!</label>
					<div>
						<textarea class="bookly-js-user-notes" rows="3"></textarea>
					</div>
				</div>
			</div>
		</div>


		<div class="bookly-box bookly-nav-steps">
			<button class="bookly-back-step bookly-js-back-step bookly-btn ladda-button" data-style="zoom-in" data-spinner-size="40" ps-submit-button-id="_ps-rs5ex90r3" style="border: 2px dashed red;">
				<span class="ladda-label">Vorige</span>
			</button>
			<button class="bookly-next-step bookly-js-next-step bookly-btn ladda-button" data-style="zoom-in" data-spinner-size="40" ps-submit-button-id="_ps-54gwmr1gc" style="border: 2px dashed red;">
				<span class="ladda-label">Volgende</span>
			</button>
		</div></div>
</div>

</body>
</html>
