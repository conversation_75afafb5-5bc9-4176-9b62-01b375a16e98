/*
 ProveSource JS SDK
 Copyright 2018 ProveSource
 */

(function() {

		//Bypassing mootools assholes
		if (!JSON.parse && JSON.decode) JSON.parse = JSON.decode;
		if (!JSON.stringify && JSON.encode) JSON.stringify = JSON.encode;
		//
		if (window.proofsrc) {
			window.provesrc = window.proofsrc;
		}
		if (window.provesrc && window.provesrc.constants && window.provesrc.constants.scriptLoaded) {
			return;
		}


		var localStorageRef = localStorage || window.localStorage;
		var root = this;
		var _psNotificationsQueue = [];
		var _psIsRunning = false;
		var _psIsShowingNotification = false;
		var _psThisNotification;
		var _psCheckDocumentReadyState;
		var _psLivePingIntervalTimer;
		var _psShowTimeoutTimer;
		var _psCloseTimeoutTimer;
		var _psAPIServer = "http://localhost:3000";
		var _psDisplayQueue = window.dq || [];

		if (window.provesrc && window.provesrc.dq) _psDisplayQueue = window.provesrc.dq;
		if (window.proofsrc && window.proofsrc.dq) _psDisplayQueue = window.proofsrc.dq;

		window.proofsrc = window.provesrc = root.provesrc = {
			psThemeStyle: _psGetWidgetStyle(),
			//--------Utils
			utils: {
				compileTemplate: function (template, params) {
					var keys = Object.keys(params);
					var output = template;
					for (var i = 0; i < keys.length; i += 1) {
						var key = keys[i];
						var regex = new RegExp('{{' + key + '}}', 'g');
						output = output.replace(regex, params[key]);
					}
					return output;
				},
				toBase64: function (str) {
					return btoa(unescape(encodeURIComponent(str)));
				},
				brandingEnforcement: function () {
					provesrc.debug("Starting branding enforcement...");
					setInterval(function () {
						if (document.querySelector("#provesrc-notification-container .pfs-link")) {
							document.querySelector("#provesrc-notification-container .pfs-link").style.setProperty("display", "inline-block", "important");
							document.querySelector("#provesrc-notification-container .pfs-link").style.setProperty("opacity", "1", "important");
							const bgColor = _psThisNotification.settings.theme && _psThisNotification.settings.theme.backgroundColor ? _psThisNotification.settings.theme.backgroundColor : '#fff';
							let brandingColor = getColorContrast(bgColor, 60, 80);
							if (_psThisNotification) {
								if (_psThisNotification.type == provesrc.constants.notificationTypes.COMBO) {
									brandingColor = _psThisNotification.settings.theme.titleColor;
									document.querySelector("#provesrc-notification-container .pfs-link > a").innerHTML = " ProveSource";
								} else if (_psThisNotification.type == provesrc.constants.notificationTypes.SOCIAL || _psThisNotification.type == provesrc.constants.notificationTypes.INFORMATIONAL || _psThisNotification.type == provesrc.constants.notificationTypes.REVIEW) {
									document.querySelector("#provesrc-notification-container .pfs-link > a").innerHTML = " ProveSource";
								} else {
									document.querySelector("#provesrc-notification-container .pfs-link > a").innerHTML = " ProveSource";
								}
							}

							document.querySelector("#provesrc-notification-container .pfs-link > a").style.setProperty("color", brandingColor, "important");
							document.querySelector("#provesrc-notification-container .pfs-link > a").style.setProperty("display", "inline-block", "important");
							document.querySelector("#provesrc-notification-container .pfs-link > a").style.setProperty("opacity", "1", "important");
							document.querySelector("#provesrc-notification-container .pfs-link > a").style.setProperty("font-size", "11px", "important");
						}
					}, 1000);
				},
				hasCustomBranding: function () {
					return provesrc.settings.whitelabel || (provesrc.settings.whitelabel && provesrc.settings.branding && provesrc.settings.branding.active);
				},
				setSegmentUserId: function (obj) {
					var userId = this.getSegmentUserId() || provesrc.constants.segmentUserId;
					if (userId) {
						obj.segmentUserId = userId;
					}
					return obj;
				},
				getSegmentUserId: function () {
					if (!window.analytics) {
						return null;
					}
					if (analytics.user) {
						var user = analytics.user();
						if (user) {
							var userId = analytics.user().id();
							if (!userId) {
								userId = analytics.user().anonymousId();
								provesrc.debug("[getSegmentUserId] Found anonymousId");
							} else {
								provesrc.debug("[getSegmentUserId] Found userId");
							}
							provesrc.constants.segmentUserId = userId;
							return userId;
						}
						return null;
					}
				},
				initSegmentIO: function () {
					if (window.analytics && analytics.ready) {
						analytics.ready(function () {
							provesrc.debug("[initSegmentIO] Analytics.js is ready, getting userId...");
							this.getSegmentUserId();
						}.bind(this));
					}
				},
				currentTime: function () {
					return new Date().getTime();
				},
				getExpiryTime: function (timeToAppend) {
					var uniqueEvent = new Date();
					uniqueEvent.setDate(uniqueEvent.getDate() + timeToAppend);
					return uniqueEvent.getTime();
				},
				isMobile: function () {
					return provesrc.getViewportSize()[0] <= provesrc.constants.mobileScreenSize;
				},
				isIE: function () {
					var ua = window.navigator.userAgent;
					var msie = ua.indexOf('MSIE ');
					if (msie > 0) {
						// IE 10 or older => return version number
						return parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);
					}

					var trident = ua.indexOf('Trident/');
					if (trident > 0) {
						// IE 11 => return version number
						var rv = ua.indexOf('rv:');
						return parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);
					}

					var edge = ua.indexOf('Edge/');
					if (edge > 0) {
						// Edge (IE 12+) => return version number
						return parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);
					}
					// other browser
					return false;
				},
				UUID: function (get) {
					if (get) {
						return provesrc.storage.get("xuuid");
					} else {
						var dec2hex = [];
						for (var i = 0; i <= 15; i++) {
							dec2hex[i] = i.toString(16);
						}

						var uuid = '';
						for (var i = 1; i <= 36; i++) {
							if (i === 9 || i === 14 || i === 19 || i === 24) {
								uuid += '-';
							} else if (i === 15) {
								uuid += 4;
							} else if (i === 20) {
								uuid += dec2hex[(Math.random() * 4 | 0 + 8)];
							} else {
								uuid += dec2hex[(Math.random() * 15 | 0)];
							}
						}
						return uuid;
					}
				},
				get: function get(obj, key, defaultValue) {
					try {
						var comps = key.split('.');
						var object = obj;
						for (var i = 0; i < comps.length; i += 1) {
							object = object && object[comps[i]];
						}
						return object || defaultValue || null;
					} catch (err) {
					}
					return null;
				},
				getDarkerColor: function (color, percent = 20) {
					color = color.replace(/^#/, '');
					let num = parseInt(color, 16);
					let r = (num >> 16) & 0xFF;
					let g = (num >> 8) & 0xFF;
					let b = num & 0xFF;
					r /= 255;
					g /= 255;
					b /= 255;
					let max = Math.max(r, g, b), min = Math.min(r, g, b);
					let h, s, l = (max + min) / 2;

					if (max === min) {
						h = s = 0;
					} else {
						let d = max - min;
						s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
						switch (max) {
							case r:
								h = (g - b) / d + (g < b ? 6 : 0);
								break;
							case g:
								h = (b - r) / d + 2;
								break;
							case b:
								h = (r - g) / d + 4;
								break;
						}
						h /= 6;
					}
					l = Math.max(0, l - percent / 100);
					let q = l < 0.5 ? l * (1 + s) : l + s - l * s;
					let p = 2 * l - q;

					function hue2rgb(p, q, t) {
						if (t < 0) t += 1;
						if (t > 1) t -= 1;
						if (t < 1 / 6) return p + (q - p) * 6 * t;
						if (t < 1 / 2) return q;
						if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
						return p;
					}

					let r2, g2, b2;
					if (s === 0) {
						r2 = g2 = b2 = l; // achromatic
					} else {
						r2 = hue2rgb(p, q, h + 1 / 3);
						g2 = hue2rgb(p, q, h);
						b2 = hue2rgb(p, q, h - 1 / 3);
					}
					r2 = Math.round(r2 * 255);
					g2 = Math.round(g2 * 255);
					b2 = Math.round(b2 * 255);

					return (
						'#' +
						((1 << 24) + (r2 << 16) + (g2 << 8) + b2)
							.toString(16)
							.slice(1)
							.toUpperCase()
					);
				},
				getLighterColor: function (color, percent = 20) {
					color = color.replace(/^#/, '');
					let num = parseInt(color, 16);
					let r = (num >> 16) & 0xFF;
					let g = (num >> 8) & 0xFF;
					let b = num & 0xFF;
					r /= 255;
					g /= 255;
					b /= 255;
					let max = Math.max(r, g, b), min = Math.min(r, g, b);
					let h, s, l = (max + min) / 2;

					if (max === min) {
						h = s = 0;
					} else {
						let d = max - min;
						s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
						switch (max) {
							case r:
								h = (g - b) / d + (g < b ? 6 : 0);
								break;
							case g:
								h = (b - r) / d + 2;
								break;
							case b:
								h = (r - g) / d + 4;
								break;
						}
						h /= 6;
					}
					l = l + (1 - l) * (percent / 100);
					let q = l < 0.5 ? l * (1 + s) : l + s - l * s;
					let p = 2 * l - q;

					function hue2rgb(p, q, t) {
						if (t < 0) t += 1;
						if (t > 1) t -= 1;
						if (t < 1 / 6) return p + (q - p) * 6 * t;
						if (t < 1 / 2) return q;
						if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
						return p;
					}

					let r2, g2, b2;
					if (s === 0) {
						r2 = g2 = b2 = l; // achromatic
					} else {
						r2 = hue2rgb(p, q, h + 1 / 3);
						g2 = hue2rgb(p, q, h);
						b2 = hue2rgb(p, q, h - 1 / 3);
					}
					r2 = Math.round(r2 * 255);
					g2 = Math.round(g2 * 255);
					b2 = Math.round(b2 * 255);

					return (
						'#' +
						((1 << 24) + (r2 << 16) + (g2 << 8) + b2)
							.toString(16)
							.slice(1)
							.toUpperCase()
					);
				},
				isColorLight: function (hex) {
					hex = hex.replace(/^#/, '');
					if (hex.length === 3) {
						hex = hex.split('').map(x => x + x).join('');
					}
					var r = parseInt(hex.substr(0, 2), 16);
					var g = parseInt(hex.substr(2, 2), 16);
					var b = parseInt(hex.substr(4, 2), 16);
					var brightness = (r * 299 + g * 587 + b * 114) / 1000;
					return brightness > 186;
				},
				hexToRgba: function (hex, alpha = 1) {
					hex = hex.replace(/^#/, '');
					if (hex.length === 3) {
						hex = hex.split('').map(x => x + x).join('');
					}
					const num = parseInt(hex, 16);
					const r = (num >> 16) & 255;
					const g = (num >> 8) & 255;
					const b = num & 255;
					return `rgba(${r},${g},${b},${alpha})`;
				}
			},
			session: {
				storage: {
					init: function () {

						if (getLocalStorage()) {
							if (!sessionStorage.length) {
								// Ask other tabs for session storage
								localStorageSetItem(provesrc.storage.getKeyString('getSessionStorage'), Date.now());
							}
						}

						if (getLocalStorage()) {
							window.addEventListener('storage', function (event) {
								//console.log('storage event', event);
								if (event.key == provesrc.storage.getKeyString('getSessionStorage')) {
									// Some tab asked for the sessionStorage -> send it
									localStorageSetItem(provesrc.storage.getKeyString('sessionStorage'), JSON.stringify(sessionStorage));
									localStorageRemoveItem(provesrc.storage.getKeyString('sessionStorage'));
								} else if (event.key == provesrc.storage.getKeyString('sessionStorage') && !sessionStorage.length) {
									// sessionStorage is empty -> fill it
									var data = JSON.parse(event.newValue);
									for (key in data) {
										sessionStorageSetItem(key, data[key]);
									}
								}
							});
						}


						window.onbeforeunload = function () {
							// sessionStorage.clear();
						};

					},
					didViewNotification: function (n, justCheck) {
						var nId = n._id;
						if (n.type == provesrc.constants.notificationTypes.STREAM || n.type == provesrc.constants.notificationTypes.REVIEW || (n.data && n.data.id)) {
							nId = n.data.id;
						}
						var viewEventKey = provesrc.storage.getKeyString(provesrc.constants.storageNotificationViewSession) + nId;
						if (getLocalStorage()) {
							if (sessionStorageGetItem(viewEventKey)) {
								provesrc.debug("[session didViewNotification] Notification", nId, "was already viewed in this session, removing from Q");
								provesrc.Q.remove(n);
								return true;
							} else {
								if (!justCheck) {
									sessionStorageSetItem(viewEventKey, true);
								}
								return false;
							}
						}

					},
					setNotificationClosed: function () {
						sessionStorageSetItem(provesrc.constants.storageHideSessionNotifications, true);
					},
					notificationsDisabledInSession: function () {
						return sessionStorageGetItem(provesrc.constants.storageHideSessionNotifications);
					}
				},
				didScanForms: false
			},
			//--------General Constants
			constants: {
				scriptLoaded: true,
				psNotificationContainerId: "provesrc-notification-container",
				stylesBlockId: "_psStylesBlock",
				customCSSBlockId: "_psCustomCSSBlock",
				storagePrefix: 'provesrc.',
				storageNotificationViewSession: 'session.view.',
				storageFirstPartyHeader: 'x-ps-first',
				storageHideSessionNotifications: 'hide-session-notifications',
				storageConversionsKey: "conversion",
				storageAnalyticsKey: "analytics",
				storageFirstTimeVisitorKey: "first-time-visitor",
				storageEngagedVisitorKey: "engaged-visitor",
				storageConversionAnalyticsKey: "events-form-conversions",
				storageGoalsTrackingKey: "ps-goals",
				planLimitRemarketingLink: "https://console.provesrc.com/#/billing/upgrade?utm_source=plan-limit-remarketing&utm_medium=in-website-remarketing&utm_campaign=provesrc",
				utmMedium: "notification",
				utmCampaign: "powered-by-provesrc",
				notificationClickUTM: {
					utmSource: "provesource",
					utmMedium: "provesource-notification",
					utmCampaign: "click"
				},
				psElements: {
					ignoreForm: "ps-ignore-form",
					form: "ps-dynamic-form-id",
					emailField: "ps-email-field-id",
					submit: "ps-submit-button-id",
					name: "ps-name-field-id",
					firstName: "ps-firstname-field-id",
					lastName: "ps-lastname-field-id"
				},
				livePingInterval: 120 * 1000, //2 minutes
				mobileScreenSize: 815,
				optOutTime: 7, //days
				uniqueUserTime: 30, //days
				engagedUniqueUserTime: 30, //days
				analyticsUniqueEventTime: 7, //days
				analyticsConversionActionEventTime: 3, //days
				planLimitRemarketingTime: 7, //days
				fieldRegex: {
					email: /(email|e-mail|מייל)/ig,
					firstName: /(first_)|(value="Name)|(member_name)|(Naam)|(voornaam)|((?:^|\W)Nume(?:$|\W))|(Jméno)|(Imię)|(имя)|(prénom)|(prenom)|(first[-_ ]name)|(your[-_]name)|(name[-_ ]first)|(name  first)|(firstname)|(fname)|(full[-_ ]?name)|(cust[-_]name)|(customername)|(custname)|(customer[_-]name)|(contactname)|(contact[_-]name)|("prenom")|(subscriber[s]?[_-]name)|(nombre)|(nome)|(purchase_name)|(given-name)|(form-field-name)|(פרטי)/ig,
					lastName: /(last[-_ ]name)|(name[-_ ]last)|(name  last)|(lastname)|(surname)|(sur[-_ ]name)|("lname")|("nom")|(cognome)|(family-name)|(משפחה)/ig
				},
				events: {
					impression: "impression",
					conversion: "conversion"
				},
				notificationTypes: {
					COMBO: 'combo',
					STREAM: 'stream',
					LIVE: 'live',
					INFORMATIONAL: 'info',
					REVIEW: 'review',
					SOCIAL: 'social'
				},
				socialNetworks: {
					FACEBOOK: 'facebook',
					TWITTER: 'twitter',
					YOUTUBE: 'youtube',
					INSTAGRAM: 'instagram'
				},
				analyticsEvents: {
					hover: 'hover',
					view: 'view',
					click: 'click',
					visitor: 'visitor'
				},
				analyticsProviders: {
					GoogleAnalytics: "googleAnalytics",
					Mixpanel: "mixpanel",
					Amplitude: "amplitude"
				}
			},

			//-------General Settings
			settings: {
				sdkVersion: "4.7.11",
				animations: {
					top: {
						show: "psBounceInDown",
						close: "psFadeOutUp"
					},
					bottom: {
						show: "psBounceInUp",
						close: "psFadeOutDown"
					}
				},
				zindex: {mobile: 99, desktop: 99},
				debugMode: false,
				dashboardDebugMode: false,
				consolePromotion: true,
				apiServer: _psAPIServer,
				firstShowDelay: 0,
				displayHold: 8,
				delayBetweenNotifications: 5,
				trackers: {}
			},
			//-------Form constants
			formTypes: {
				convertful: {
					forms: ["div.conv-container", "form.conv-form", "div.conv-col", "div.conv-widget .conv-widget-step"],
					submit: ["a.conv-btn.conv_action_submit"]
				},
				beaver_builder: {
					forms: ["div.uabb-form.uabb-subscribe-form"],
					submit: ["div.uabb-form-button a.uabb-button"]
				},
				clickfunnels: {
					forms: [".clickfunnels-com #modalPopup", ".clickfunnels-com .containerInner"],
					submit: ["a[href='#submit-form'], a[href='#submit-form-2step'], a[href='#reveal-paypal'], div[data-imagelink='#submit-form']"]
				},
				paperform: {
					forms: [".editor.editor--live.Paperform__Container"],
					submit: ["form.Paperform__QuestionBlock div.submit"]
				},
				infusionsoft: {
					forms: ["form#orderForm"],
					submit: ["#CHECKOUT_LINKS a.continueButton"]
				},
				activtrial: {
					forms: [".bl-block-content-table > .bl-block-content-row"],
					submit: [".bl-block-content-item.bl-block-content-item-button"]
				},
				quickcheckout: {
					forms: ["#d_quickcheckout"],
					submit: ["#confirm_wrap button#qc_confirm_order"]
				},
				thrivecart: {
					forms: ["body.thrive-mode-checkout #body-content", "div.checkout-panes"],
					submit: ["#form-order button.button-submit"]
				},
				opencart: {
					forms: ["#maincontent"],
					submit: ["#simplecheckout_button_confirm"]
				},
				gohighlevel: {
					forms: ["#appointment_widgets--revamp"],
					submit: ["#schedule-meeting-button"]
				},
				others: {
					forms: ["div.cp-form-container", "#order-standard_cart", "#reservation_form", "#order", "#edd_checkout_form_wrap"],
					submit: ["div.cp-submit", "#btnCompleteOrdersidebar", "a.btn.submit_form", "div.col > button.btn.btn-primary", "#edd-purchase-button"]
				},
				regular: {
					forms: ["form"],
					submit: ["a.wsite-button", "button[type=submit]", "input[type=submit]", "div[onclick]", "a[onclick]", "button.form-submit", "div.pay"]
				},
				iframe: {
					forms: ["form"],
					submit: ["a.wsite-button", "button[type=submit]", "input[type=submit]", "div[onclick]", "a[onclick]"]
				}
			},
			//-------API Manager
			API: {
				reporters: {
					ping: {
						reportPing: function () {
							provesrc.API.req(provesrc.API.endpoints.livePing, 'POST', {}, function (err, res, data) {
								provesrc.debug('[API reporters][PING]: Reported.');
							});
						},
						initPing: function () {
							if (provesrc.settings.hasLive && !_psLivePingIntervalTimer) {
								provesrc.API.reporters.ping.reportPing();
								provesrc.debug('[API reporters][PING]: Calling /notifications/ping every 30 seconds');
								_psLivePingIntervalTimer = setInterval(function () {
									provesrc.API.reporters.ping.reportPing();
								}, provesrc.constants.livePingInterval);
							} else {
								_psLivePingIntervalTimer = null;
								provesrc.debug('[API reporters][PING]: Not sending ping');
							}
						}
					}
				},
				headers: {
					applicationJson: "application/json",
					firstPartyHeader: "x-ps-first",
					formDataEncoded: "application/x-www-form-urlencoded; charset=UTF-8"
				},
				endpoints: {
					livePing: _psAPIServer + "/notifications/ping",
					getNotifications: _psAPIServer + "/notifications/get",
					trackFormEvent: _psAPIServer + "/events/trackForm",
					getConfig: _psAPIServer + "/account/configuration?url=",
					analytics: _psAPIServer + "/notifications/analytics",
					goals: _psAPIServer + "/goals/analytics"
				},
				req: function (url, method, params, cb) {

					if (provesrc.settings.blocked) return;

					var reqParams = {
						mode: 'cors',
						credentials: 'include',
						method: method,
						headers: {
							'Content-Type': this.headers.applicationJson,
							'Accept': 'application/json',
							'Authorization': "Bearer " + provesrc.settings.apiKey,
							'x-ps-uid': provesrc.utils.UUID(true),
							'x-ps-url': provesrc.utils.toBase64(provesrc.URL.getCurrentURL()),
							'x-ps-version': provesrc.settings.sdkVersion
						}
					};

					var firstPartyHeaderValue = getPSfirstHeader();
					if (firstPartyHeaderValue) {
						reqParams.headers[this.headers.firstPartyHeader] = firstPartyHeaderValue;
					}

					//Handle response
					function handleResponse(err, res, data, psFirstHeader) {
						handlePSFirstHeader(psFirstHeader);
						return cb(err, res, data);
					}

					//Handle x-ps-first header
					function getPSfirstHeader() {
						var result = "";
						var cookieFromStorage = provesrc.storage.get(provesrc.constants.storageFirstPartyHeader, false);
						if (cookieFromStorage && cookieFromStorage.length) {
							return cookieFromStorage;
						}

						var cookies = document.cookie.split(";");
						if (!cookies || cookies.length == 0) {
							provesrc.debug("[X] [getPSfirstHeader]: NO cookies to send in x-ps-first");
							return null;
						}

						for (var x = 0; x < cookies.length; x++) {
							var cookie = cookies[x].replace(/\s/g, '');
							if (cookie.substr(0, 2) == "ps") {
								result += cookie + ";";
							}
						}

						provesrc.debug("[√] [getPSfirstHeader]: Created x-ps-first header value:", result);
						return result.length > 0 ? result : null;
					}

					function handlePSFirstHeader(psFirstHeader) {
						if (!psFirstHeader || psFirstHeader.length == 0) {
							provesrc.debug("[handleResponse]: NO x-ps-first header");
							return;
						}

						provesrc.debug("[handleResponse]: Got x-ps-first header, parsing...", psFirstHeader);
						provesrc.debug("[handlePSFirstHeader]: x-ps-first =", psFirstHeader);

						var parsedHeader = psFirstHeader.split("$");
						provesrc.debug("[handlePSFirstHeader]: Parsed header:", parsedHeader);
						parsedHeader = appendCookiesDomain(parsedHeader);
						setPSFirstCookies(parsedHeader);

						var cookieValue = [];
						for (var i = 0; i < parsedHeader.length; i++) {
							var kv = parsedHeader[i].split(";");
							if (kv.length) {
								cookieValue.push(kv[0]);
							}
						}

						if (cookieValue.length) {
							provesrc.storage.set(provesrc.constants.storageFirstPartyHeader, cookieValue.join(";"), false);
							provesrc.debug("[handlePSFirstHeader]: Saved first party cookie header in localStorage");
						}

					}

					function appendCookiesDomain(cookies) {
						var rootDomain = provesrc.URL.getRootDomain();
						for (var i = 0; i < cookies.length; i++) {
							cookies[i] += ";domain=" + rootDomain;
						}
						provesrc.debug("[appendCookiesDomain]: Added domain to cookies:", cookies);
						return cookies;
					}

					function setPSFirstCookies(cookies) {
						var rootDomain = provesrc.URL.getRootDomain();
						provesrc.debug("[setPSFirstCookies]: Setting cookies on domain:", rootDomain);
						for (var i = 0; i < cookies.length; i++) {
							document.cookie = cookies[i];
						}
						provesrc.debug("[setPSFirstCookies]: Done setting first party cookies on domain");
					}


					// Execute request
					if (window.fetch && window.fetch.toString().indexOf("[native code]") > -1) {
						if (method == "POST") reqParams.body = JSON.stringify(params);
						window.fetch(url, reqParams).then(function (response) {
							return response.json().then(function (data) {
								if (response.ok) {
									return handleResponse(null, response, data, response.headers.get(provesrc.API.headers.firstPartyHeader));
								} else {
									return Promise.reject(new Error(response.statusText))
								}
							});
						}).catch(function (err) {
							provesrc.debug('PS:', err);
						});

					} else {

						var xhttp = new XMLHttpRequest();
						xhttp.open(method, url, true);
						xhttp.withCredentials = true;
						xhttp.setRequestHeader('Content-type', this.headers.applicationJson);
						xhttp.setRequestHeader('Accept', 'application/json');
						xhttp.setRequestHeader('Authorization', "Bearer " + provesrc.settings.apiKey);
						xhttp.setRequestHeader('x-ps-uid', provesrc.utils.UUID(true));
						xhttp.setRequestHeader('x-ps-url', provesrc.utils.toBase64(provesrc.URL.getCurrentURL()));
						xhttp.setRequestHeader('x-ps-version', provesrc.settings.sdkVersion);
						if (firstPartyHeaderValue) {
							xhttp.setRequestHeader('x-ps-first', firstPartyHeaderValue);
						}

						xhttp.onreadystatechange = function () {
							if (xhttp.readyState == 4 && xhttp.status == 200) {
								if (xhttp.response) {
									var parsedResponse = JSON.parse(xhttp.response);
									return handleResponse(null, xhttp, parsedResponse, xhttp.getResponseHeader(provesrc.API.headers.firstPartyHeader));
								} else {
									return handleResponse(null, xhttp, {}, xhttp.getResponseHeader(provesrc.API.headers.firstPartyHeader));
								}
							} else if (xhttp.status != 200) {
								new Error(xhttp.response);
							}
						};
						if (method == "POST") xhttp.send(JSON.stringify(params));
						else xhttp.send();
					}
				}
			},
			//-------QUEUE MANAGER
			Q: {
				add: function (arr) {
					provesrc.debug("[Q add]: Adding to Q");
					Array.prototype.push.apply(_psNotificationsQueue, arr);
				},
				push: function (itm) {
					provesrc.debug("[Q push]: Pushing to Q");
					_psNotificationsQueue.push(itm);
				},
				pull: function () {
					provesrc.debug("[Q pull]: Pulling from Q");
					return _psNotificationsQueue.shift();
				},
				remove: function (itm) {
					provesrc.debug("[Q remove]: Removing from Q");
					_psNotificationsQueue.splice(_psNotificationsQueue.indexOf(itm), 1);
				},
				status: function () {
					return _psNotificationsQueue.length
				},
				isEmpty: function () {
					return _psNotificationsQueue.length == 0
				},
				reset: function () {
					provesrc.debug("[Q reset]: Resetting Q");
					_psNotificationsQueue = [];
				}
			},
			//-------LocalStorage/Cookies Helper
			storage: {
				initNewVisitor: function () {
					var firstTimeVisitor = provesrc.storage.get(provesrc.constants.storageFirstTimeVisitorKey);
					var timestamp = provesrc.utils.currentTime();
					if (!firstTimeVisitor) {
						provesrc.debug("[Visitor Type] This is a first time visitor, setting timestamp", timestamp);
						provesrc.storage.set(provesrc.constants.storageFirstTimeVisitorKey, timestamp);
						sessionStorageSetItem(provesrc.constants.storageFirstTimeVisitorKey, true);
					} else {
						provesrc.debug("[Visitor Type] This is a returning visitor");
					}
				},
				isNewVisitor: function () {
					if (sessionStorageGetItem(provesrc.constants.storageFirstTimeVisitorKey)) {
						return null;
					}
					return provesrc.storage.get(provesrc.constants.storageFirstTimeVisitorKey);
				},
				cookies: {
					set: function (name, value, expires, path, domain) {
						document.cookie = name + "=" + escape(value) +
							((expires) ? "; expires=" + new Date(new Date().getTime() + 60 * 60 * 1000 * 24 * expires).toGMTString() : "") +
							((path) ? "; path=" + path : "") +
							((domain) ? "; domain=" + domain : "") +
							"; SameSite=None; secure";
					},
					get: function (name) {
						var cookie = " " + document.cookie;
						var search = " " + name + "=";
						var setStr = null;
						var offset = 0;
						var end = 0;
						if (cookie.length > 0) {
							offset = cookie.indexOf(search);
							if (offset != -1) {
								offset += search.length;
								end = cookie.indexOf(";", offset)
								if (end == -1) {
									end = cookie.length;
								}
								setStr = unescape(cookie.substring(offset, end));
							}
						}
						return (setStr);
					}
				},
				getCookie: function (name) {
					var match = document.cookie.match(new RegExp('(?:^|;\\s*)' + name + '=([^;]*)'));
					return match ? match[1] : null;
				},
				getKeyString: function (key) {
					return provesrc.constants.storagePrefix + key;
				},
				set: function (key, value, saveJson) {
					if (getLocalStorage()) {
						if (saveJson) localStorageSetItem(this.getKeyString(key), JSON.stringify(value));
						else localStorageSetItem(this.getKeyString(key), value);
					}
				},
				get: function (key, getJson) {
					if (getLocalStorage()) {
						if (getJson) return JSON.parse(localStorageGetItem(this.getKeyString(key)));
						else return localStorageGetItem(this.getKeyString(key));
					} else return null;
				},
				del: function (key) {
					if (getLocalStorage()) localStorageRemoveItem(this.getKeyString(key));
				},
				searchKey: function (term) {
					if (getLocalStorage()) {
						var searchTerm = provesrc.constants.storagePrefix + term;
						provesrc.debug("Searching localStorage for the term", searchTerm);
						var currentKey, results = [];
						for (currentKey in localStorageRef) {
							if (currentKey.indexOf(searchTerm) != -1) {
								results.push({
									key: currentKey.replace(searchTerm + ".", ""),
									val: localStorageGetItem(currentKey)
								})
							}
						}
						return results;
					} else {
						return null;
					}
				},
				clearOldStorage: function () {
					if (getLocalStorage()) {
						var keys = Object.keys(localStorageRef);
						for (var x = 0; x < keys.length; x++) {
							if (keys[x].indexOf('provesrc.visited.') > -1) {
								localStorageRemoveItem(keys[x]);
							}
						}
					}
				}
			},

			//-------Helpers
			debug: function (message) {
				var consoleId = "[*] [ProveSource] ";
				// var consoleId = "[*] [" + new Date().toISOString() + "] ProveSource: ";
				message = consoleId += message;
				if (provesrc.settings.debugMode) {
					if (window.Debug && window.Debug.writeln) {
						window.Debug.writeln(message);
					} else if (window.console && window.console.log && typeof window.console.log === 'function') {
						window.console.log.apply(window.console, arguments);
					}
				}
			},
			log: function (message) {
				if (!provesrc.settings.consolePromotion) return;

				var consoleId = "[*] ProveSource: ";
				message = consoleId += message;
				if (window.Debug && window.Debug.writeln) {
					window.Debug.writeln(message);
				} else if (window.console && window.console.log && typeof window.console.log === 'function') {
					window.console.log.apply(window.console, arguments);
				}
			},
			getViewportSize: function () {
				var e, g, doc;
				try {
					doc = top.document.documentElement;
					g = (e = top.document.body) && top.document.clientWidth && top.document.clientHeight;
				} catch (e) {
					doc = document.documentElement;
					g = (e = document.body) && document.clientWidth && document.clientHeight;
				}
				var vp = [];
				doc && doc.clientWidth && doc.clientHeight && ("CSS1Compat" == document.compatMode || !g) ? vp = [doc.clientWidth, doc.clientHeight] : g && (vp = [doc.clientWidth, doc.clientHeight]);
				return vp;
			},
			//-------URL Helpers
			URL: {
				listeners: {
					tracked: {},
					track: function (id) {
						provesrc.URL.listeners.tracked[id] = true;
					},
					isTracked: function (id) {
						return provesrc.URL.listeners.tracked[id];
					}
				},
				setupListeners: function () {
					provesrc.debug("Setup window URL listeners");

					let prevUrl = window.location.href;
					setInterval(() => {
						const currUrl = window.location.href;
						if (currUrl != prevUrl) {
							// URL changed
							provesrc.debug(`URL changed from ${prevUrl} to ${currUrl}`);
							prevUrl = currUrl;
							executeURLChange();
						}
					}, 60);

					// Handle page visibility change events
					if (document.visibilityState) {
						provesrc.debug("Setup visibility change listener");
						document.addEventListener('visibilitychange', handleVisibilityChange, false);

						function handleVisibilityChange() {
							provesrc.debug("Page visibilitychange event fired:", document.visibilityState);
							if (document.visibilityState == "hidden") {
								clearInterval(_psLivePingIntervalTimer);
								_psLivePingIntervalTimer = null;
								provesrc.HTML.manageMouseHover(true)
							} else {
								provesrc.API.reporters.ping.initPing(); //resume
								provesrc.HTML.manageMouseHover(false)
							}
						}
					}

					// Handle URL change events
					function executeURLChange() {
						_psIsRunning = false;
						_psIsShowingNotification = false;
						provesrc.debug("URL has changed");
						provesrc.Q.reset();
						clearTimeout(_psShowTimeoutTimer);
						clearTimeout(_psCloseTimeoutTimer);
						clearInterval(_psLivePingIntervalTimer);
						_psLivePingIntervalTimer = null;
						provesrc.HTML.removeContainer();
						provesrc.launch();
					}
				},
				setupFormsSubmissionListener: function () {
					if (provesrc.settings.stopFormTracking) {
						provesrc.debug("[setupFormsSubmissionListener] Form auto tracking is disabled");
						return;
					}
					var readyStateCheck = setInterval(function () {
						provesrc.debug("Page ready state:", document.readyState);
						if (document.readyState == "complete" || document.readyState == "interactive") {
							clearInterval(readyStateCheck);
							provesrc.debug("Clearing readyStateCheck");
							provesrc.debug("DOM fully loaded and parsed, searching forms");
							searchPageForms();
							formsDriller();
						}
					}, 250);
				},
				generateBrandingLink: function (n) {
					var brandingLink = null;

					if (provesrc.settings.whitelabel
						&& provesrc.settings.branding
						&& provesrc.settings.branding.active
						&& provesrc.settings.branding.link
						&& provesrc.settings.branding.link.length > 0) {

						brandingLink = provesrc.settings.branding.link;

					} else if (!provesrc.settings.whitelabel) {
						brandingLink = "https://provesrc.com/verified?src=" + provesrc.URL.getHostname()
							+ "&utm_source=" + this.getCurrentURL()
							+ "&utm_medium=" + provesrc.constants.utmMedium
							+ "&utm_campaign=" + provesrc.constants.utmCampaign
							+ "&utm_content=" + n._id
							+ "&utm_term=" + n.name;

						if (provesrc.settings.affiliateLink && provesrc.settings.affiliateId) {
							brandingLink += "&aff=" + provesrc.settings.affiliateId;
						}
					}

					return brandingLink;
				},
				generateLinkUTMParams: function (n, link) {
					var nLink = provesrc.utils.get(n, 'settings.link') || {active: false, addUTMParams: false};
					if (!nLink || !nLink.active || !nLink.addUTMParams) {
						return "";
					}

					var starter = "?";
					if (link) {
						if (link.indexOf("?") != -1) {
							starter = "&";
						}
					}
					return starter + "utm_source=" + provesrc.constants.notificationClickUTM.utmSource
						+ "&utm_medium=" + provesrc.constants.notificationClickUTM.utmMedium
						+ "&utm_campaign=" + provesrc.constants.notificationClickUTM.utmCampaign
						+ "&utm_content=" + n._id
						+ "&utm_term=" + n.name
						+ "&utm_path=" + window.location.pathname
				},
				getRootDomain: function () {
					var i = 0, domain = document.domain, p = domain.split('.'), s = '_gd' + (new Date()).getTime();
					while (i < (p.length - 1) && document.cookie.indexOf(s + '=' + s) == -1) {
						domain = p.slice(-1 - (++i)).join('.');
						document.cookie = s + "=" + s + ";domain=" + domain + ";";
					}
					document.cookie = s + "=;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=" + domain + ";";
					return domain;
				},
				getCurrentURL: function () {
					return window.location.href && window.location.href.substring(0, 1500);
				},
				getHostname: function () {
					return window.location.hostname;
				},
				isURLUniqueVisitor: function () {
					provesrc.debug("Checking if user is a unique visitor at", this.getCurrentURL());
					var currentKey = "visits";

					function generateTimestamp() {
						var uniqueVisitorTime = new Date();
						uniqueVisitorTime.setDate(uniqueVisitorTime.getDate() + provesrc.constants.uniqueUserTime);
						return uniqueVisitorTime.getTime();
					}

					var visits = {};
					var hasVisits = provesrc.storage.get(currentKey);
					if (hasVisits) {
						visits = provesrc.storage.get(currentKey, true);
					}
					var returningVisitor = visits[window.location.href];
					var currentTime = new Date().getTime();

					if (returningVisitor) {
						if (returningVisitor > currentTime) {
							provesrc.debug("User is not unique in this URL");
							return false;
						} else {
							provesrc.debug("User is returning but unique again, updating timestamp.");
							visits[window.location.href] = generateTimestamp();
							provesrc.storage.set(currentKey, visits, true);
						}
					} else {
						provesrc.debug("User is new and unique in this URL, updating timestamp.");
						visits[window.location.href] = generateTimestamp();
						provesrc.storage.set(currentKey, visits, true);
					}

					return true;
				}
			},
			VISIBILITY: {
				_psCloseNotification: function (nId, event) {
					provesrc.debug("[_psCloseNotification] Notification Closed:", nId);
					if (nId == 'planlimit') {
						var uniqueEvent = new Date();
						uniqueEvent.setDate(uniqueEvent.getDate() + provesrc.constants.planLimitRemarketingTime);
						var futureTime = uniqueEvent.getTime();
						provesrc.storage.set("hide-planlimit-remarketing", futureTime);
					} else if (nId == 'remarketing') {
						provesrc.storage.set("hide-remarketing", true);
					} else {
						if (provesrc.settings.allowOptOut) {
							this._psOptOut();
						} else {
							provesrc.debug("[_psCloseNotification] Hiding all notifications in current session");
							provesrc.session.storage.setNotificationClosed();
						}
					}

					provesrc.HTML.removeContainer();
					event.stopPropagation()
				},
				_psOptOut: function (customDays) {
					var optOutFor = provesrc.settings.optOutDays || provesrc.constants.optOutTime;
					if (customDays) {
						optOutFor = customDays;
					}
					var optOutTime = new Date();
					optOutTime.setDate(optOutTime.getDate() + optOutFor);
					var optOutKey = "optout." + window.location.hostname; //Opt out from whole domain.
					var optOutValue = optOutTime.getTime(); //How much time until we opt in.
					provesrc.debug("Opting out user until", optOutTime, "(", optOutFor, "days)");
					provesrc.storage.set(optOutKey, optOutValue);
				},
				isOptedOut: function () {
					var currentHostname = window.location.hostname;
					var isOptedOut = provesrc.storage.get("optout." + currentHostname);
					var currentTime = new Date().getTime();
					if (isOptedOut) {
						if (isOptedOut > currentTime) {
							provesrc.debug("User is currently opted out on this domain till", new Date(Number(isOptedOut)));
							return true;
						} else {
							provesrc.storage.del("optout." + currentHostname);
						}
						return false;
					}
					return false;
				}
			},
			//-------DOM Helpers
			DOM: {
				observe: function (elem, isIframe) {
					// if (isIframe) {
					// 	provesrc.debug("[DOM observe]: Running DOM observer on an IFRAME");
					// }
					// if(window.grecaptcha) {
					// 	provesrc.debug("[DOM.observe] Not observing form elements due to grecaptcha on page");
					// }
					if (elem) {
						var MutationObserver = window.MutationObserver || window.WebKitMutationObserver,
							eventListenerSupported = window.addEventListener;

						if (MutationObserver) {
							var obs = new MutationObserver(function (mutations, observer) {
								if (mutations[0].addedNodes.length || mutations[0].removedNodes.length)
									DOMChangeEventCallback(mutations);
							});
							obs.observe(elem, {childList: true, subtree: true});
						} else if (eventListenerSupported) {
							elem.addEventListener('DOMNodeInserted', DOMChangeEventCallback, false);
							elem.addEventListener('DOMNodeRemoved', DOMChangeEventCallback, false);
						}
					}
				}
			},
			//-------Template Helpers
			HTML: {
				removeContainer: function () {
					var containerNode = document.getElementById("provesrc-widget-area");
					if (containerNode) {
						if (containerNode.remove) containerNode.remove();
						else if (containerNode && containerNode.outerHTML) containerNode.outerHTML = "";
					}
				},
				injectContainer: function (notificationHTML, cb) {
					var newDiv = document.createElement('div');
					newDiv.id = "provesrc-widget-area";
					newDiv.innerHTML = notificationHTML;
					document.body.appendChild(newDiv);
					cb();
				},
				injectCustomCSS: function (customCSS) {
					if (!customCSS || customCSS.length === 0) return;

					var css = document.createElement('style');
					css.id = provesrc.constants.customCSSBlockId;
					if (css.styleSheet) css.styleSheet.cssText = customCSS; // Support for IE
					else css.appendChild(document.createTextNode(customCSS)); // Support for the rest
					document.getElementsByTagName("head")[0].insertAdjacentElement('beforeend', css)
				},
				injectCustomCode: function (customCode) {
					if (!customCode || customCode.length === 0) return;
					var script = document.createElement('script');
					script.id = 'ps-custom-code'
					script.textContent = "try { " + customCode + " } catch (err){provesrc.debug('CUSTOM CODE ERROR:', err)}";
					document.head.appendChild(script);
				},
				injectStyles: function () {
					var hasStyles = document.getElementById(provesrc.constants.stylesBlockId);
					if (hasStyles) {
						provesrc.debug("[injectStyles] Current page has PS styles");
						return;
					}
					provesrc.debug("[injectStyles] Injecting styles");
					//Font

					if (!provesrc.settings.blockGoogleFonts) {
						var fontNode = document.createElement("link");
						fontNode.rel = "stylesheet";
						fontNode.type = "text/css";
						fontNode.media = "all";
						fontNode.href = "https://fonts.googleapis.com/css?family=Lato:400,700,900";
						document.getElementsByTagName("head")[0].insertAdjacentElement('beforeend', fontNode)
					}

					//Styles
					var css = document.createElement('style');
					css.type = 'text/css';
					css.id = provesrc.constants.stylesBlockId;
					if (css.styleSheet) css.styleSheet.cssText = getZindexOverride(provesrc.psThemeStyle); // Support for IE
					else css.appendChild(document.createTextNode(getZindexOverride(provesrc.psThemeStyle))); // Support for the rest
					document.getElementsByTagName("head")[0].insertAdjacentElement('beforeend', css)

					function getZindexOverride(style) {
						var zindex = provesrc.settings.zindex;
						var zmobile = zindex.mobile || 99;
						var zdesktop = zindex.desktop || 99;
						style += "#provesrc-notification-container {z-index: " + zdesktop + ";}";
						style += "#provesrc-widget-area {z-index: " + zdesktop + ";}";
						style += "@media only screen and (max-width: 815px) {#provesrc-notification-container {z-index: " + zmobile + ";}}";
						style += "@media only screen and (max-width: 815px) {#provesrc-widget-area {z-index: " + zmobile + ";}}";
						return style;
					}

				},
				setNotificationSettingsAfterLoad: function (n) {
					if (n.settings.theme) {
						provesrc.debug("Updating notification styles");
						var psLink = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-time .pfs-link")[0];
						var psLinkSpan = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-time .pfs-link span")[0];
						var psLinkBrand = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-time .pfs-link a")[0];
						var psCheckIcon = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-time .pfs-link svg > g")[0];
						var notificationBody = document.querySelectorAll("#provesrc-notification-container .bubble-body")[0];
						var description = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-description")[0];
						var notificationTime = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-time")[0];
						var bubbleIcon = document.querySelectorAll("#provesrc-notification-container .bubble-icon")[0];
						var bubbleTitle = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-content .ps-bubble-title")[0];
						if (notificationBody) {
							if (n.settings.theme.useGradientBackground) {
								const deg = n.settings.rtl ? 270 : 90;
								var darker = provesrc.utils.getDarkerColor(n.settings.theme.backgroundColor, 15);
								notificationBody.style.background = 'linear-gradient( ' + deg + 'deg, ' + n.settings.theme.backgroundColor + ', ' + darker + ')';
							} else {
								notificationBody.style.backgroundColor = n.settings.theme.backgroundColor || "#FFFFFF";
							}
							notificationBody.style.borderRadius = n.settings.rounded ? n.settings.rounded + "px" : 0 + "px";
						}

						if (bubbleIcon) {
							bubbleIcon.style.display = n.settings.showImage ? "" : "none";
						}
						if (bubbleTitle) {
							bubbleTitle.style.display = n.settings.showTitle ? "" : "none";
						}
						if (notificationTime) {
							var timeSpan = notificationTime.querySelector('span');
							if (timeSpan) {
								let color = n.settings.theme.dateColor || n.settings.theme.title.color
								if (typeof n.settings.showDate !== 'undefined') {
									timeSpan.style.display = n.settings.showDate ? "inline" : "none";
								}
								if(n.type == provesrc.constants.notificationTypes.REVIEW){
									color = n.settings.theme.messageColor
								}
								timeSpan.style.setProperty("color", color, "important");
							}
						}
						//STREAM
						if (n.type == provesrc.constants.notificationTypes.STREAM) {

							var psProductLink = document.querySelectorAll("#provesrc-notification-container #ps-stream-product-link")[0];
							var image = document.querySelectorAll("#provesrc-notification-container .bubble-icon img")[0];
							var bubbleContent = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-content-inner")[0];
							if (bubbleContent) {
								if (!n.settings.showImage) {
									if (n.settings.rtl) {
										bubbleContent.style.marginRight = (n.settings.rounded < 10 ? 10 : n.settings.rounded) + 'px';
									} else {
										bubbleContent.style.marginLeft = (n.settings.rounded < 10 ? 10 : n.settings.rounded) + 'px';
									}
								} else {
									if (n.settings.rtl) {
										bubbleContent.style.paddingRight = '7px'
									} else {
										bubbleContent.style.paddingLeft = '7px'
									}
								}
							}
							if (image) {
								image.style.borderRadius = n.settings.rounded * 2 + "px";
							}
							if (description) {
								description.style.setProperty("color", n.settings.theme.messageColor || n.settings.theme.title.color, "important");
								var descriptionSpans = description.getElementsByTagName('span');
								for (var i = 0; i < descriptionSpans.length; i++) {
									descriptionSpans[i].style.setProperty("color", n.settings.theme.messageColor || n.settings.theme.title.color, "important");
								}
							}
							if (n.data && n.data.product) {
								if (psProductLink && n.data.product.link) {
									psProductLink.style.setProperty("color", n.settings.theme.title.color, "important");
									psProductLink.addEventListener("click", function (event) {
										event.preventDefault();
										event.stopPropagation();
										provesrc.ANALYTICS.track(provesrc.constants.analyticsEvents.click, n._id, n);
										provesrc.debug("Product link clicked");
										var linkOpen = n.data.product.link;
										linkOpen += provesrc.URL.generateLinkUTMParams(n, linkOpen);
										if (!n.settings.disableProductLink || !provesrc.settings.forceNoProductLinks) {
											var productlink = provesrc.utils.get(n, 'settings.link', {});
											if (productlink.active && productlink && productlink.newTab) {
												window.open(linkOpen, '_blank');
											} else {
												window.location.href = linkOpen;
											}
										}

										return false;
									});

									var link = provesrc.utils.get(n, 'settings.link', {});
									var addparams = link.addUTMParams || false;
									var openInNewTab = link.newTab || false;
									var linkVal = link.value;
									if (link && link.active) {
										if (n.settings.platform === 'custom' && linkVal) {
											n.settings.link = {
												active: true,
												newTab: openInNewTab,
												value: linkVal,
												addUTMParams: addparams
											};
										} else if (!n.settings.disableProductLink || !provesrc.settings.forceNoProductLinks) {
											n.settings.link = {
												active: true,
												newTab: openInNewTab,
												value: provesrc.utils.get(n, 'data.product.link'),
												addUTMParams: addparams
											};
										}
									}
								}
							}

							if (provesrc.settings.whitelabel && provesrc.settings.branding && provesrc.settings.branding.active) {
								if (provesrc.settings.branding.color) {
									psLinkBrand.style.setProperty("color", provesrc.settings.branding.color, "important");

									if (psCheckIcon) psCheckIcon.style.setProperty("fill", provesrc.settings.branding.color, "important");
								}
							}

						}

						if (n.type == provesrc.constants.notificationTypes.SOCIAL) {
							var psSocialIcons = document.querySelectorAll("#provesrc-notification-container .social-item img");
							if (psSocialIcons && psSocialIcons.length > 0) {
								for (var i = 0; i < psSocialIcons.length; i++) {
									psSocialIcons[i].style.borderRadius = n.settings.rounded + "px";
								}
							}
							if (description) {
								description.style.setProperty("color", n.settings.theme.titleColor, "important");
							}
						}
						//COMBO
						if (n.type == provesrc.constants.notificationTypes.COMBO) {
							var notificationBody = document.querySelectorAll("#provesrc-notification-container .bubble-body")[0];
							var description = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-description")[0];
							var comboNumber = document.querySelectorAll("#provesrc-notification-container .bubble-body .combo-number")[0];
							var notificationTime = document.querySelectorAll("#provesrc-notification-container .bubble-body .bubble-time")[0];
							var closeBtnBefore = document.querySelectorAll("#ps-bubble-close .close-before")[0];
							var closeBtnAfter = document.querySelectorAll("#ps-bubble-close .close-after")[0];
							var bubbleIcon = document.querySelectorAll("#provesrc-notification-container .bubble-icon")[0];

							notificationBody.style.backgroundColor = n.settings.theme.backgroundColor || "#FFFFFF";
							if (typeof n.settings.rounded !== 'undefined') {
								notificationBody.style.borderRadius = n.settings.rounded + "px";
							}

							if (bubbleIcon && typeof n.settings.showImage !== 'undefined') {
								bubbleIcon.style.display = n.settings.showImage ? "block" : "none";
								if (!n.settings.showImage) {
									description.style.marginLeft = "10px";
								}
							}

							comboNumber.style.color = n.settings.theme.titleColor || n.settings.theme.title.color;
							description.style.setProperty("color", n.settings.theme.messageColor || n.settings.theme.title.color, "important");

							if (notificationTime) {
								if (typeof n.settings.showDate !== 'undefined') {
									notificationTime.style.display = n.settings.showDate ? "block" : "none";
								}
								notificationTime.style.color = n.settings.theme.dateColor || n.settings.theme.title.color;
							}

							if (comboNumber) {
								for (var i = 0; i < comboNumber.children.length; i++) {
									comboNumber.children[i].style.setProperty("color", n.settings.theme.titleColor || n.settings.theme.title.color, "important");
								}
							}

							if (description) {
								for (var b = 0; b < description.children.length; b++) {
									description.children[b].style.setProperty("color", n.settings.theme.messageColor || n.settings.theme.title.color, "important");
								}
							}

							if (n.settings.allowClose) {
								closeBtnBefore.style.backgroundColor = n.settings.theme.titleColor || n.settings.theme.title.color;
								closeBtnAfter.style.backgroundColor = n.settings.theme.titleColor || n.settings.theme.title.color;
							}

							if (!provesrc.settings.whitelabel) {
								psLinkBrand.style.setProperty("color", n.settings.theme.titleColor || n.settings.theme.title.color, "important");
								if (psCheckIcon) psCheckIcon.style.setProperty("fill", n.settings.theme.titleColor || n.settings.theme.title.color, "important");

								psLinkBrand.style.backgroundColor = n.settings.theme.backgroundColor || n.settings.theme.title.backgroundColor;
								psLink.style.color = n.settings.theme.titleColor || n.settings.theme.title.color;
								psLinkSpan.style.color = n.settings.theme.titleColor || n.settings.theme.title.color;
								psLink.style.backgroundColor = n.settings.theme.backgroundColor || n.settings.theme.title.backgroundColor;
								psLink.style.borderColor = n.settings.theme.titleColor || n.settings.theme.title.color;

							} else if (provesrc.settings.whitelabel && provesrc.settings.branding && provesrc.settings.branding.active) {
								psLink.style.backgroundColor = n.settings.theme.backgroundColor || n.settings.theme.title.backgroundColor;
								psLink.style.color = n.settings.theme.titleColor || n.settings.theme.title.color;
								psLinkSpan.style.color = n.settings.theme.titleColor || n.settings.theme.title.color;
								psLink.style.borderColor = n.settings.theme.titleColor || n.settings.theme.title.color;
								psLinkBrand.style.setProperty("color", n.settings.theme.titleColor || n.settings.theme.title.color, "important");
								if (psCheckIcon) psCheckIcon.style.setProperty("fill", n.settings.theme.titleColor || n.settings.theme.title.color, "important");

							}
						} else {
							var notificationTitle = document.getElementsByClassName("ps-bubble-title")[0];
							if (notificationTitle) {
								notificationTitle = document.getElementsByClassName("ps-bubble-title")[0].getElementsByTagName('span')[0];
								notificationTitle.style.color = n.settings.theme.titleColor || n.settings.theme.title.color;
							}

							if (provesrc.settings.whitelabel && provesrc.settings.branding && provesrc.settings.branding.active) {
								if (provesrc.settings.branding.color) {
									psLinkBrand.style.setProperty("color", provesrc.settings.branding.color, "important");
									if (psCheckIcon) psCheckIcon.style.setProperty("fill", provesrc.settings.branding.color, "important");

								}
							}
						}
					}


					document.getElementById("provesrc-bubble-body-container").onclick = function (ev) {
						provesrc.ANALYTICS.track(provesrc.constants.analyticsEvents.click, n._id, n);
						var link = provesrc.utils.get(n, 'settings.link') || {
							active: false,
							value: null,
							addUTMParams: false,
							newTab: false
						};
						if (link && link.active && link.value) {
							provesrc.debug("Setting up notification link", link.value);
							provesrc.debug("Open in new tab?", link.newTab);
							var linkOpen = link.value;
							if (link.addUTMParams) {
								provesrc.debug("Append UTM Params");
								linkOpen += provesrc.URL.generateLinkUTMParams(n, linkOpen)
							}
							provesrc.debug("Open link", linkOpen);
							if (link.newTab) {
								window.open(linkOpen);
							} else {
								window.location.href = linkOpen;
							}
							ev.stopPropagation();
						}

					};

					if (n.type == provesrc.constants.notificationTypes.COMBO || n.type == provesrc.constants.notificationTypes.LIVE) {
						if (n.settings && (!n.settings.hideExactNumber || !n.settings.hideExactNumber.active)) {
							animateValue(n.count, n.type);
						}
					}

					function animateValue(end, type) {
						var targetElement = null;
						if (type == provesrc.constants.notificationTypes.COMBO) {
							targetElement = document.querySelectorAll("#provesrc-notification-container .num-value")[0];
						} else if (type == provesrc.constants.notificationTypes.LIVE) {
							targetElement = document.querySelectorAll("#provesrc-notification-container .ps-bubble-title > span")[0];
						}

						var options = {
							useEasing: true,
							useGrouping: true,
							separator: ',',
							decimal: '.'
						};

						var countAnimation = new CountUp(targetElement, 1, end, 0, 2.5, options);
						if (!countAnimation.error) {
							countAnimation.start();
						} else {
							console.error(countAnimation.error);
						}
					}
				},
				generateNotificationHTML: function (params) {

					function localizedString(key, value) {
						var code = params.localization;

						if (!code) code = 'en';

						if (value == 'day') {
							value = 24;
						} else if (value == 'week') {
							value = 7;
							key = 'LAST_DAYS';
						} else if (value == 'month') {
							value = 30;
							key = 'LAST_DAYS';
						} else if (value == 'all') {
							value = "[time]";
							key = 'LAST_DAYS';
						}


						var langDict = {
							en: {
								SOMEONE: "Someone",
								RECENTLY: "Recently",
								BY: "by",
								PAST_MINUTES: "in the past %d minutes",
								PAST_HOURS: "in the past %d hours",
								PAST_DAYS: "in the past %d days",
								LAST_HOURS: "in the last %d hours",
								LAST_DAYS: "in the last %d days",
								JUST_NOW: "Just now",
								VERIFIED_BY: "verified by",
								VISITED_PAGE: "visited this page",
								PAST_HOUR: "in the past hour",
								SINCE_YESTERDAY: "since yesterday",
								SINCE_LAST_WEEK: "since last week",
								PAST_WEEKS: "in the past %d weeks",
								SINCE_LAST_MONTH: "since last month",
								PAST_MONTHS: "in the past %d months",
								SINCE_LAST_YEAR: "since last year",
								PAST_YEARS: "in the past %d years",
								ONLINE: "online"
							},
							da: {
								SOMEONE: "Nogen",
								RECENTLY: "for nylig",
								BY: "af",
								PAST_MINUTES: "i de sidste %d minutter",
								PAST_HOURS: "i de sidste %d timer",
								PAST_DAYS: "i løbet af de seneste %d dage",
								LAST_HOURS: "i de sidste %d timer",
								LAST_DAYS: "i de sidste %d dage",
								JUST_NOW: "lige nu",
								VERIFIED_BY: "bekræftet af",
								VISITED_PAGE: "besøgte denne side",
								PAST_HOUR: "i den seneste time",
								SINCE_YESTERDAY: "siden igår",
								SINCE_LAST_WEEK: "siden sidste uge",
								PAST_WEEKS: "i de sidste %d uger",
								SINCE_LAST_MONTH: "siden sidste måned",
								PAST_MONTHS: "i de sidste %d måneder",
								ONLINE: "online"
							},
							lv: {
								SOMEONE: "Kāds",
								RECENTLY: "Nesen",
								BY: "no",
								PAST_MINUTES: "pēdējo %d minūšu laikā",
								PAST_HOURS: "pēdējo %d stundu laikā",
								PAST_DAYS: "pēdējo %d dienu laikā",
								LAST_HOURS: "pēdējo %d stundu laikā",
								LAST_DAYS: "pēdējo %d dienu laikā",
								JUST_NOW: "Tikko",
								VERIFIED_BY: "verificēja",
								VISITED_PAGE: "apmeklēja šo lapu",
								PAST_HOUR: "pēdējās stundas laikā",
								SINCE_YESTERDAY: "kopš vakardienas",
								SINCE_LAST_WEEK: "kopš pagājušās nedēļas",
								PAST_WEEKS: "pēdējo %d nedēļu laikā",
								SINCE_LAST_MONTH: "kopš pagājušā mēneša",
								PAST_MONTHS: "pēdējo %d mēnešu laikā",
								SINCE_LAST_YEAR: "kopš pagājušā gada",
								PAST_YEARS: "pēdējo %d gadu laikā",
								ONLINE: "tiešsaistē"
							},
							fr: {
								SOMEONE: "Quelqu'un",
								RECENTLY: "récemment",
								BY: "par",
								PAST_MINUTES: "dans les %d dernières minutes",
								PAST_HOURS: "dans les %d dernières heures",
								PAST_DAYS: "au cours des %d derniers jours",
								LAST_HOURS: "au cours des %d dernières heures",
								LAST_DAYS: "au cours des %d derniers jours",
								JUST_NOW: "à l'instant",
								VERIFIED_BY: "vérifié par",
								VISITED_PAGE: "ont visité cette page",
								PAST_HOUR: "dans l'heure écoulée",
								SINCE_YESTERDAY: "depuis hier",
								SINCE_LAST_WEEK: "depuis la semaine dernière",
								PAST_WEEKS: "au cours des %d dernières semaines",
								SINCE_LAST_MONTH: "depuis le mois dernier",
								PAST_MONTHS: "au cours des %d derniers mois",
								ONLINE: "en ligne"
							},
							he: {
								SOMEONE: "מישהו",
								RECENTLY: "לאחרונה",
								BY: "על ידי",
								PAST_MINUTES: "ב-%d הדקות האחרונות",
								PAST_HOURS: "ב-%d השעות האחרונות",
								PAST_DAYS: "ב-%d הימים האחרונים",
								LAST_HOURS: "ב-%d השעות האחרונות",
								LAST_DAYS: "ב-%d הימים האחרונים",
								JUST_NOW: "בדיוק עכשיו",
								VERIFIED_BY: "מאומת על ידי",
								VISITED_PAGE: "ביקרו בעמוד זה",
								PAST_HOUR: "בשעה האחרונה",
								SINCE_YESTERDAY: "מאתמול",
								SINCE_LAST_WEEK: "מאז השבוע שעבר",
								PAST_WEEKS: "ב-%d השבועות האחרונים",
								SINCE_LAST_MONTH: "מאז החודש שעבר",
								PAST_MONTHS: "ב-%d החודשים האחרונים",
								ONLINE: "אונליין"
							},
							hi: {
								SOMEONE: "कोई व्यक्ति",
								RECENTLY: "हाल ही में",
								BY: "द्वारा",
								PAST_MINUTES: "पिछले %d मिनट में",
								PAST_HOURS: "पिछले %d घंटों में",
								PAST_DAYS: "पिछले %d दिनों में",
								LAST_HOURS: "पिछले %d घंटों में",
								LAST_DAYS: "पिछले %d दिनों में",
								JUST_NOW: "अभी",
								VERIFIED_BY: "प्रोवेसोर्स द्वारा",
								VISITED_PAGE: "इस पृष्ठ का दौरा किया",
								PAST_HOUR: "पिछले घंटे में",
								SINCE_YESTERDAY: "कल से",
								SINCE_LAST_WEEK: "पिछले हफ्ते से",
								PAST_WEEKS: "पिछले %d सप्ताह में",
								SINCE_LAST_MONTH: "पिछले महीने से",
								PAST_MONTHS: "पिछले %d महीनों में",
								ONLINE: "ऑनलाइन"
							},
							id: {
								SOMEONE: "seseorang",
								RECENTLY: "Baru saja",
								BY: "oleh",
								PAST_MINUTES: "Dalam %d menit yang lalu",
								PAST_HOURS: "Dalam %d jam yang lalu",
								PAST_DAYS: "Dalam %d hari yang lalu",
								LAST_HOURS: "Dalam %d jam terakhir",
								LAST_DAYS: "Dalam %d hari terakhir",
								JUST_NOW: "Saat ini",
								VERIFIED_BY: "diverifikasi oleh",
								VISITED_PAGE: "mengunjungi halaman ini",
								PAST_HOUR: "Dalam satu jam yang lalu",
								SINCE_YESTERDAY: "Sejak kemarin",
								SINCE_LAST_WEEK: "Sejak minggu lalu",
								PAST_WEEKS: "Dalam %d minggu terakhir",
								SINCE_LAST_MONTH: "Sejak bulan lalu",
								PAST_MONTHS: "Dalam %d bulan yang lalu",
								ONLINE: "on line"
							},
							nb: {
								SOMEONE: "noen",
								RECENTLY: "nylig",
								BY: "av",
								PAST_MINUTES: "i løpet av de siste %d minuttene",
								PAST_HOURS: "i løpet av de siste %d timene",
								PAST_DAYS: "i løpet av de siste %d dagene",
								LAST_HOURS: "i de siste %d timene",
								LAST_DAYS: "i de siste %d dagene",
								JUST_NOW: "akkurat nå",
								VERIFIED_BY: "Bekreftet av",
								VISITED_PAGE: "besøkte denne siden",
								PAST_HOUR: "i den siste timen",
								SINCE_YESTERDAY: "siden i går",
								SINCE_LAST_WEEK: "siden forrige uke",
								PAST_WEEKS: "i de siste %d ukene",
								SINCE_LAST_MONTH: "siden forrige måned",
								PAST_MONTHS: "i de siste %d månedene",
								ONLINE: "på nett"
							},
							sv: {
								SOMEONE: "Någon",
								RECENTLY: "Nyligen",
								BY: "med",
								PAST_MINUTES: "under de senaste %d minuterna",
								PAST_HOURS: "under de senaste %d timmarna",
								PAST_DAYS: "under de %d senaste dagarna",
								LAST_HOURS: "under de %d senaste timmarna",
								LAST_DAYS: "under de %d senaste dagarna",
								JUST_NOW: "Just nu",
								VERIFIED_BY: "Verifierad av",
								VISITED_PAGE: "Besökte denna sidan",
								PAST_HOUR: "under den senaste timmen",
								SINCE_YESTERDAY: "Sedan igår",
								SINCE_LAST_WEEK: "Sedan förra veckan",
								PAST_WEEKS: "under de %d senaste veckorna",
								SINCE_LAST_MONTH: "Den senaste månaden",
								PAST_MONTHS: "under de senaste %d månaderna",
								ONLINE: "uppkopplad"
							},
							vi: {
								SOMEONE: "Người nào",
								RECENTLY: "mới đây",
								BY: "bởi",
								PAST_MINUTES: "trong %d phút qua",
								PAST_HOURS: "trong %d giờ qua",
								PAST_DAYS: "trong %d ngày qua",
								LAST_HOURS: "trong %d giờ trước đây",
								LAST_DAYS: "trong %d ngày trước đây",
								JUST_NOW: "vừa nãy",
								VERIFIED_BY: "được xác minh bởi",
								VISITED_PAGE: "đã truy cập trang này",
								PAST_HOUR: "trong 1 giờ qua",
								SINCE_YESTERDAY: "từ hôm qua",
								SINCE_LAST_WEEK: "kể từ tuần trước",
								PAST_WEEKS: "trong %d tuần trước",
								SINCE_LAST_MONTH: "kể từ tháng trước",
								PAST_MONTHS: "trong %d tháng qua",
								ONLINE: "Trực tuyến"
							},
							hr: {
								SOMEONE: "neko",
								RECENTLY: "nedavno",
								BY: "by",
								PAST_MINUTES: "u zadnjih %dmin",
								PAST_HOURS: "u zadnjih %dh",
								PAST_DAYS: "u zadnjih %d dana",
								LAST_HOURS: "u posljednjih %dh",
								LAST_DAYS: "u posljednjih %d dana",
								JUST_NOW: "upravo sada",
								VERIFIED_BY: "potvrđeno od",
								VISITED_PAGE: "posjetilo je ovu stranicu",
								PAST_HOUR: "u zadnjih 1h",
								SINCE_YESTERDAY: "od jučer",
								SINCE_LAST_WEEK: "u prošlih tjedan dana",
								PAST_WEEKS: "u posljednjih %d tjedana",
								SINCE_LAST_MONTH: "u prošlih mjesec dana",
								PAST_MONTHS: "u posljednjih %d mjeseci",
								ONLINE: "en línia"
							},
							bn: {
								SOMEONE: "কেউ",
								RECENTLY: "সম্প্রতি",
								BY: "দ্বারা",
								PAST_MINUTES: "গত %d মিনিটে",
								PAST_HOURS: "গত %d ঘন্টা",
								PAST_DAYS: "গত %d দিনে",
								LAST_HOURS: "শেষ %d ঘন্টা",
								LAST_DAYS: "শেষ %d দিনে",
								JUST_NOW: "এক্ষুনি",
								VERIFIED_BY: "দ্বারা প্রমাণিত",
								VISITED_PAGE: "এই পৃষ্ঠাটি পরিদর্শন করেছেন",
								PAST_HOUR: "শেষ এক ঘন্টায়",
								SINCE_YESTERDAY: "গতকাল থেকে",
								SINCE_LAST_WEEK: "গত সপ্তাহ থেকে",
								PAST_WEEKS: "শেষ %d সপ্তাহ",
								SINCE_LAST_MONTH: "গত মাস থেকে",
								PAST_MONTHS: "শেষ %d মাস",
								ONLINE: "অনলাইন"
							},
							nl: {
								SOMEONE: "Iemand",
								RECENTLY: "Onlangs",
								BY: "door",
								PAST_MINUTES: "in de afgelopen %d minuten",
								PAST_HOURS: "in de afgelopen %d uren",
								PAST_DAYS: "in de afgelopen %d dagen",
								LAST_HOURS: "in de laatste %d uren",
								LAST_DAYS: "in de laatste %d dagen",
								VERIFIED_BY: "geverifieerd door",
								VISITED_PAGE: "Hebben deze pagina bezocht",

								JUST_NOW: "Zojuist",
								PAST_HOUR: "in het afgelopen uur",
								SINCE_YESTERDAY: "Sinds gisteren",
								SINCE_LAST_WEEK: "Sinds vorige week",
								PAST_WEEKS: "in de afgelopen %d weken",
								SINCE_LAST_MONTH: "Sinds vorige maand",
								PAST_MONTHS: "in de afgelopen %d maanden",
								ONLINE: "online"
							},
							pt: {
								SOMEONE: "Alguém",
								RECENTLY: "recentemente",
								BY: "por",
								PAST_MINUTES: "nos últimos %d minutos",
								PAST_HOURS: "nas últimas %d horas",
								PAST_DAYS: "nos últimos %d dias",
								LAST_HOURS: "nas últimas %d horas",
								LAST_DAYS: "nos últimos %d dias",
								VERIFIED_BY: "Verificado por",
								VISITED_PAGE: "visitaram essa página",

								JUST_NOW: "neste momento",
								PAST_HOUR: "na última hora",
								SINCE_YESTERDAY: "desde ontem",
								SINCE_LAST_WEEK: "desde a semana passada",
								PAST_WEEKS: "nas últimas %d semanas",
								SINCE_LAST_MONTH: "desde o mês passado",
								PAST_MONTHS: "nos últimos %d meses",
								ONLINE: "conectados"
							},
							es: {
								SOMEONE: "Alguien",
								RECENTLY: "recientemente",
								BY: "por",
								PAST_MINUTES: "en los últimos %d minutos",
								PAST_HOURS: "en las últimas %d horas",
								PAST_DAYS: "en los últimos %d días",
								LAST_HOURS: "en las últimas %d horas",
								LAST_DAYS: "en los últimos %d días",
								VERIFIED_BY: "verificado por",
								VISITED_PAGE: "han visitado esta página",

								JUST_NOW: "ahora",
								PAST_HOUR: "en la última hora",
								SINCE_YESTERDAY: "desde ayer",
								SINCE_LAST_WEEK: "desde la semana pasada",
								PAST_WEEKS: "en las últimas %d semanas",
								SINCE_LAST_MONTH: "desde el mes pasado",
								PAST_MONTHS: "en los últimos %d meses",
								ONLINE: "en línea"
							},
							ru: {
								SOMEONE: "Кто то",
								RECENTLY: "за последнее время",
								BY: "сделано",
								PAST_MINUTES: "за последние %d минут",
								PAST_HOURS: "за последние %d часов",
								PAST_DAYS: "за последние %d дней",
								LAST_HOURS: "за последние %d часов",
								LAST_DAYS: "за последние %d дней",
								VERIFIED_BY: "проверено",
								VISITED_PAGE: "посетили эту страницу",

								JUST_NOW: "только что",
								PAST_HOUR: "за последний час",
								SINCE_YESTERDAY: "со вчерашнего дня",
								SINCE_LAST_WEEK: "за последнюю неделю",
								PAST_WEEKS: "за последние %d недель",
								SINCE_LAST_MONTH: "за последний месяц",
								PAST_MONTHS: "за последние %d месяцев",
								ONLINE: "онлайн"
							},
							de: {
								SOMEONE: "Jemand",
								RECENTLY: "vor kurzem",
								BY: "von",
								PAST_MINUTES: "in den letzten %d Minuten",
								PAST_HOURS: "in den letzten %d Stunden",
								PAST_DAYS: "in den letzten %d Tagen",
								LAST_HOURS: "in den letzten %d Stunden",
								LAST_DAYS: "in den letzten %d Tagen",
								VERIFIED_BY: "verifiziert von",
								VISITED_PAGE: "besuchten diese Seite",

								JUST_NOW: "gerade eben",
								PAST_HOUR: "in der letzten Stunde",
								SINCE_YESTERDAY: "seit gestern",
								SINCE_LAST_WEEK: "seit letzter Woche",
								PAST_WEEKS: "in den letzten %d Wochen",
								SINCE_LAST_MONTH: "seit letztem Monat",
								PAST_MONTHS: "in den letzten %d Monaten",
								SINCE_LAST_YEAR: "innerhalb des letzten Jahres",
								ONLINE: "online"
							},
							it: {
								SOMEONE: "Qualcuno",
								RECENTLY: "recentemente",
								BY: "da",
								PAST_MINUTES: "negli ultimi %d minuti",
								PAST_HOURS: "nelle ultime %d ore",
								PAST_DAYS: "negli ultimi %d giorni",
								LAST_HOURS: "nelle ultime %d ore",
								LAST_DAYS: "negli ultimi %d giorni",
								VERIFIED_BY: "verificato da",
								VISITED_PAGE: "hanno visitato questa pagina",

								JUST_NOW: "adesso",
								PAST_HOUR: "nell'ultima ora",
								SINCE_YESTERDAY: "da ieri",
								SINCE_LAST_WEEK: "dalla scorsa settimana",
								PAST_WEEKS: "nelle ultime %d settimane",
								SINCE_LAST_MONTH: "dallo scorso mese",
								PAST_MONTHS: "negli ultimi %d mesi",
								ONLINE: "in linea"
							},
							pl: {
								SOMEONE: "Ktoś",
								RECENTLY: "ostatnio",
								BY: "przez",
								PAST_MINUTES: "w ciągu ostatnich %d minut",
								PAST_HOURS: "w ciągu ostatnich %d godzin",
								PAST_DAYS: "w ciągu ostatnich %d dni",
								LAST_HOURS: "w ciągu ostatnich %d godzin",
								LAST_DAYS: "w ciągu ostatnich %d dni",
								VERIFIED_BY: "zweryfikowane przez",
								VISITED_PAGE: "odwiedziło tę stronę",

								JUST_NOW: "właśnie teraz",
								PAST_HOUR: "w ciągu ostatniej godziny",
								SINCE_YESTERDAY: "od wczoraj",
								SINCE_LAST_WEEK: "od ostatniego tygodnia",
								PAST_WEEKS: "w ciągu ostatnich %d tygodni",
								SINCE_LAST_MONTH: "od ostatniego miesiąca",
								PAST_MONTHS: "w ciągu ostatnich %d miesięcy",
								ONLINE: "online"
							},
							ja: {
								SOMEONE: "誰か",
								RECENTLY: "最近",
								BY: "から提供",
								PAST_MINUTES: "%d分前",
								PAST_HOURS: "%d時間前",
								PAST_DAYS: "%d日前",
								LAST_HOURS: "%d時前",
								LAST_DAYS: "%d日前",
								VERIFIED_BY: "が確認しました",
								VISITED_PAGE: "このページを見ました",

								JUST_NOW: "今",
								PAST_HOUR: "1時間前",
								SINCE_YESTERDAY: "昨日から",
								SINCE_LAST_WEEK: "先週から",
								PAST_WEEKS: "%d週前に",
								SINCE_LAST_MONTH: "先月から",
								PAST_MONTHS: "%dヶ月前",
								ONLINE: "オンライン"
							},
							ro: {
								SOMEONE: "Cineva",
								RECENTLY: "Recent",
								BY: "De la",
								PAST_MINUTES: "în ultimele %d minute",
								PAST_HOURS: "în ultima %d ore",
								PAST_DAYS: "în ultimele %d zile",
								LAST_HOURS: "în ultima %d ore",
								LAST_DAYS: "în ultimele %d zile",
								VERIFIED_BY: "Certificat de către",
								VISITED_PAGE: "a vizitat această pagină",

								JUST_NOW: "în prezent",
								PAST_HOUR: "în ultima oră",
								SINCE_YESTERDAY: "de ieri",
								SINCE_LAST_WEEK: "de săptămâna trecută",
								PAST_WEEKS: "în ultimele %d săptămâni",
								SINCE_LAST_MONTH: "din ultima luna",
								PAST_MONTHS: "în ultimele %d luni",
								ONLINE: "pe net"
							},
							hu: {
								SOMEONE: "Valaki",
								RECENTLY: "Nemrég",
								BY: "by",
								PAST_MINUTES: "Az elmúlt %d percben",
								PAST_HOURS: "Az elmúlt %d órában",
								PAST_DAYS: "Az elmúlt %d napon",
								LAST_HOURS: "Az elmúlt %d órában",
								LAST_DAYS: "Az elmúlt %d napon",
								VERIFIED_BY: "verified by",
								VISITED_PAGE: "látogatta meg az oldalt",

								JUST_NOW: "Mostanában",
								PAST_HOUR: "Az elmúlt órában",
								SINCE_YESTERDAY: "Tegnap óta",
								SINCE_LAST_WEEK: "Múlt hét óta",
								PAST_WEEKS: "Az elmúlt %d hétben",
								SINCE_LAST_MONTH: "Múlt hónap óta",
								PAST_MONTHS: "Az elmúlt %d hónapban",
								ONLINE: "online"
							},
							tr: {
								SOMEONE: "Birisi",
								RECENTLY: "Son zamanlarda",
								BY: "",
								PAST_MINUTES: "Son %d dakika içinde",
								PAST_HOURS: "Son %d saat içinde",
								PAST_DAYS: "Son %d gün içinde",
								LAST_HOURS: "Son %d saat içinde",
								LAST_DAYS: "Son %d gün içinde",
								VERIFIED_BY: "tarafından doğrulandı",
								VISITED_PAGE: "Bu sayfayı ziyaret etti",

								JUST_NOW: "Şu anda",
								PAST_HOUR: "Son 1 saat içinde",
								SINCE_YESTERDAY: "Dünden beri",
								SINCE_LAST_WEEK: "Geçen haftadan beri",
								PAST_WEEKS: "Son %d haftada içinde",
								SINCE_LAST_MONTH: "Geçen aydan beri",
								PAST_MONTHS: "Son %d ay içinde",
								ONLINE: "çevrimiçi"
							},
							sk: {
								SOMEONE: "Niekto",
								RECENTLY: "Nedávno",
								BY: "poskytované",
								PAST_MINUTES: "Za posledných %d minút",
								PAST_HOURS: "Za posledných %d hodín",
								PAST_DAYS: "Za posledných %d dní",
								LAST_HOURS: "V posledných %d hodinách",
								LAST_DAYS: "Za posledných %d dní",
								VERIFIED_BY: "overené",
								VISITED_PAGE: "navštívil túto stránku",

								JUST_NOW: "Teraz",
								PAST_HOUR: "Za poslednú hodinu",
								SINCE_YESTERDAY: "Od včera",
								SINCE_LAST_WEEK: "Od minulého týždňa",
								PAST_WEEKS: "V posledných %d týždňoch",
								SINCE_LAST_MONTH: "Od minulého mesiace",
								PAST_MONTHS: "Za posledné %d mesiace",
								ONLINE: "online"
							},
							cs: {
								SOMEONE: "někdo",
								RECENTLY: "nedávno",
								BY: "by",
								PAST_MINUTES: "v posledních %d minutách",
								PAST_HOURS: "v posledních %d hodinách",
								PAST_DAYS: "v posledních %d dnech",
								LAST_HOURS: "v posledních %d hodinách",
								LAST_DAYS: "v posledních %d dnech",
								VERIFIED_BY: "ověřeno",
								VISITED_PAGE: "navštívilo tuto stránku",

								JUST_NOW: "právě teď",
								PAST_HOUR: "v poslední hodině",
								SINCE_YESTERDAY: "od včerejška",
								SINCE_LAST_WEEK: "v posledním týdnu",
								PAST_WEEKS: "v posledních %d týdnech",
								SINCE_LAST_MONTH: "v posledním měsíci",
								PAST_MONTHS: "v posledních %d měsících",
								ONLINE: "online"
							},
							bg: {
								SOMEONE: "Някой",
								RECENTLY: "наскоро",
								BY: "от",
								PAST_MINUTES: "през последните %d минути",
								PAST_HOURS: "през последните %d часа",
								PAST_DAYS: "през последните %d дни",
								LAST_HOURS: "през последните %d часа",
								LAST_DAYS: "през последните %d дни",
								VERIFIED_BY: "верифицирано от",
								VISITED_PAGE: "посетиха тази страница",

								JUST_NOW: "току-що",
								PAST_HOUR: "през последния час",
								SINCE_YESTERDAY: "от вчера",
								SINCE_LAST_WEEK: "в последната седмица",
								PAST_WEEKS: "през последните %d седмици",
								SINCE_LAST_MONTH: "в последния месец",
								PAST_MONTHS: "през последните %d месеца",
								ONLINE: "онлайн"
							},
							el: {
								SOMEONE: "κάποιος",
								RECENTLY: "πρόσφατα",
								BY: "από το",
								PAST_MINUTES: "στα %d τελευταία λεπτά",
								PAST_HOURS: "τις τελευταίες %d ώρες",
								PAST_DAYS: "τις περασμένες %d ημέρες",
								LAST_HOURS: "στις %d τελευταίες ώρες",
								LAST_DAYS: "τις περασμένες %d ημέρες",
								VERIFIED_BY: "επικυρωμένο από το",
								VISITED_PAGE: "επισκέφθηκαν αυτήν τη σελίδα",
								ONLINE: "συνδεδεμένος",
								JUST_NOW: "μόλις τώρα",
								PAST_HOUR: "την τελευταία ώρα",
								SINCE_YESTERDAY: "από εχθές",
								SINCE_LAST_WEEK: "από την περασμένη εβδομάδα",
								PAST_WEEKS: "τις τελευταίες %d εβδομάδες",
								SINCE_LAST_MONTH: "από τον περασμένο μήνα",
								PAST_MONTHS: "τους %d τελευταίους μήνες"
							},
							sl: {
								SOMEONE: "Nekdo",
								RECENTLY: "pred kratkim",
								BY: "od",
								PAST_MINUTES: "v zadnjih %d minutah",
								PAST_HOURS: "v zadnjih %d urah",
								PAST_DAYS: "v zadnjih %d dneh",
								LAST_HOURS: "v zadnjih %d urah",
								LAST_DAYS: "v zadnjih %d dneh",
								VERIFIED_BY: "preverjeno od",
								VISITED_PAGE: "je obiskalo to stran",
								ONLINE: "na spletu",
								JUST_NOW: "ravnokar",
								PAST_HOUR: "v zadnji uri",
								SINCE_YESTERDAY: "od včeraj",
								SINCE_LAST_WEEK: "v zadnjem tednu",
								PAST_WEEKS: "v zadnjih %d tednih",
								SINCE_LAST_MONTH: "v zadnjem mesecu",
								PAST_MONTHS: "v zadnjih %d mesecih"
							},
							ka: {
								SOMEONE: "ვინმე",
								RECENTLY: "ბოლოს",
								BY: "",
								PAST_MINUTES: "ბოლო %d წუთის მანძილზე",
								PAST_HOURS: "ბოლო %d საათის მანძილზე",
								PAST_DAYS: "ბოლო %d დღის მანძილზე",
								LAST_HOURS: "ბოლო %d საათის მანძილზე",
								LAST_DAYS: "ბოლო %d დღის მანძილზე",
								VERIFIED_BY: "დამოწმებული",
								VISITED_PAGE: "ესტუმრა ამ გვერდს",
								ONLINE: "ონლაინში",
								JUST_NOW: "ახლა",
								PAST_HOUR: "ბოლო საათის მანძილზე",
								SINCE_YESTERDAY: "გუშინდელი დღის შემდეგ",
								SINCE_LAST_WEEK: "ბოლო კვირას",
								PAST_WEEKS: "ბოლო %d კვირის მანძილზე",
								SINCE_LAST_MONTH: "ბოლო თვეს",
								PAST_MONTHS: "ბოლო %d თვეში"
							},
							uk: {
								SOMEONE: "хтось",
								RECENTLY: "недавно",
								BY: "зроблено",
								PAST_MINUTES: "протягом останніх %d хвилин",
								PAST_HOURS: "протягом останніх %d годин",
								PAST_DAYS: "протягом останніх %d днів",
								LAST_HOURS: "протягом останніх %d годин",
								LAST_DAYS: "протягом останніх %d днів",
								VERIFIED_BY: "заьверджено",
								VISITED_PAGE: "відвідали цю сторінку",
								ONLINE: "онлайн",
								JUST_NOW: "щойно",
								PAST_HOUR: "протягом останньої години",
								SINCE_YESTERDAY: "зі вчора",
								SINCE_LAST_WEEK: "протягом останнього тижня",
								PAST_WEEKS: "протягом останніх %d тижнів",
								SINCE_LAST_MONTH: "протягом останнього місяця",
								PAST_MONTHS: "протягом останніх %d місяців"
							},

							th: {
								SOMEONE: "บางคน",
								RECENTLY: "เมื่อเร็วๆนี้",
								BY: "โดย",
								PAST_MINUTES: "ภายใน %d นาทีที่ผ่านมา",
								PAST_HOURS: "ภายใน %d ชั่วโมงที่ผ่านมา",
								PAST_DAYS: "ภายใน %d วันที่ผ่านมา",
								LAST_HOURS: "ภายใน %d ชั่วโมงที่ผ่านมา",
								LAST_DAYS: "ภายใน %d วันที่ผ่านมา",
								VERIFIED_BY: "ตรวจสอบโดย",
								VISITED_PAGE: "เข้ามาที่เพจนี้",
								ONLINE: "ออนไลน์",
								JUST_NOW: "เมื่อกี้",
								PAST_HOUR: "ภายในชั่วโมงที่ผ่านมา",
								SINCE_YESTERDAY: "ตั้งแต่เมื่อวาน",
								SINCE_LAST_WEEK: "ตั้งแต่อาทิตย์ที่แล้ว",
								PAST_WEEKS: "ภายใน %d อาทิตย์ที่ผ่านมา",
								SINCE_LAST_MONTH: "ตั้งแต่เดือนที่แล้ว",
								PAST_MONTHS: "ภายใน %d เดือนที่ผ่านมา"
							},

							ko: {
								SOMEONE: "누군가",
								RECENTLY: "최근",
								BY: "",
								PAST_MINUTES: "%d분 동안",
								PAST_HOURS: "%d시간 동안",
								PAST_DAYS: "지난 %d일 동안",
								LAST_HOURS: "지난 %d시간 동안",
								LAST_DAYS: "지난 %d일 동안",
								VERIFIED_BY: "에 의해 인증 됨",
								VISITED_PAGE: "이 페이지를 방문했습니다.",
								ONLINE: "현재 접속 중",
								JUST_NOW: "방금 전",
								PAST_HOUR: "1시간 동안",
								SINCE_YESTERDAY: "어제부터",
								SINCE_LAST_WEEK: "지난주부터",
								PAST_WEEKS: "지난 %d주 동안",
								SINCE_LAST_MONTH: "지난달부터",
								PAST_MONTHS: "지난 %d개월 동안"
							},

							ar: {
								SOMEONE: "شخص ما",
								RECENTLY: "مؤخرا",
								BY: "بواسطة",
								PAST_MINUTES: "في آخر %d دقائق",
								PAST_HOURS: "في آخر %d ساعات",
								PAST_DAYS: "في آخر %d أيام",
								LAST_HOURS: "في آخر %d ساعات",
								LAST_DAYS: "في آخر %d أيام",
								VERIFIED_BY: "موثق من قبل",
								VISITED_PAGE: "لهذه الصفحة",
								ONLINE: "أونلاين",
								JUST_NOW: "الآن",
								PAST_HOUR: "في آخر ساعة",
								SINCE_YESTERDAY: "منذ يوم أمس",
								SINCE_LAST_WEEK: "منذ الأسبوع الماضي",
								PAST_WEEKS: "في آخر %d أسابيع",
								SINCE_LAST_MONTH: "منذ الشهر الماضي",
								PAST_MONTHS: "في آخر %d شهور"
							},

							lt: {
								SOMEONE: "Kai kas",
								RECENTLY: "neseniai",
								BY: "Užpompuota",
								PAST_MINUTES: "Per paskutiniąsias %d minutes",
								PAST_HOURS: "Per paskutiniąsias %d valandas",
								PAST_DAYS: "Per paskutiniąsias %d dienas",
								LAST_HOURS: "Per paskutinąsias %d valandas",
								LAST_DAYS: "Per paskutiniąsias %d dienas",
								VERIFIED_BY: "Patvirtinta",
								VISITED_PAGE: "aplankė šį puslapį",
								ONLINE: "Prisijungę",
								JUST_NOW: "ką tik",
								PAST_HOUR: "Per paskutiniąją valandą",
								SINCE_YESTERDAY: "Nuo vakar",
								SINCE_LAST_WEEK: "Nuo praėjusios savaitės",
								PAST_WEEKS: "Per paskutiniąsias %d savaites",
								SINCE_LAST_MONTH: "Nuo praėjusio mėnesio",
								PAST_MONTHS: "Per paskutiniuosius %d mėnesnius"
							},

							fi: {
								SOMEONE: "joku",
								RECENTLY: "hiljattain",
								BY: "palvelun tarjoaa",
								PAST_MINUTES: "viimeisen %d:n minuutin aikana",
								PAST_HOURS: "viimeisen %d:n tunnin aikana",
								PAST_DAYS: "kuluneen %d:n päivän aikana",
								LAST_HOURS: "viimeisen %d:n tunnin aikana",
								LAST_DAYS: "viimeisen %d:n päivän aikana",
								VERIFIED_BY: "vahvistanut",
								VISITED_PAGE: "kävivät tällä sivulla",
								ONLINE: "paikalla",
								JUST_NOW: "juuri nyt",
								PAST_HOUR: "kuluneen tunnin aikana",
								SINCE_YESTERDAY: "eilisestä saakka",
								SINCE_LAST_WEEK: "viime viikon jälkeen",
								PAST_WEEKS: "viimeisen %d:n viikon aikana",
								SINCE_LAST_MONTH: "viime kuukauden jälkeen",
								PAST_MONTHS: "viimeisen %d:n kuukauden aikana"
							},
							'zh_cn': {
								SOMEONE: "某人",
								RECENTLY: "最近",
								BY: "由",
								PAST_MINUTES: "在過去%d分鐘內",
								PAST_HOURS: "在過去%d小時內",
								PAST_DAYS: "在過去%d天內",
								LAST_HOURS: "在最近%d小時內",
								LAST_DAYS: "在最近%d天內",
								VERIFIED_BY: "由",
								VISITED_PAGE: "訪問過這裡",
								ONLINE: "正在線上",

								JUST_NOW: "剛剛",
								PAST_HOUR: "在過去1小時內",
								SINCE_YESTERDAY: "自昨天起",
								SINCE_LAST_WEEK: "自上星期起",
								PAST_WEEKS: "在最近%d星期內",
								SINCE_LAST_MONTH: "自上個月起",
								PAST_MONTHS: "在過去%d個月內"
							},
							'zh_tw': {
								SOMEONE: "某人",
								RECENTLY: "最近",
								BY: "由",
								PAST_MINUTES: "在過去%d分鐘內",
								PAST_HOURS: "在過去%d小時內",
								PAST_DAYS: "在過去%d天內",
								LAST_HOURS: "在最近%d小時內",
								LAST_DAYS: "在最近%d天內",
								VERIFIED_BY: "由 ProveSource 驗証",
								VISITED_PAGE: "訪問過這裡",
								ONLINE: "正在線上",

								JUST_NOW: "剛剛",
								PAST_HOUR: "在過去1小時內",
								SINCE_YESTERDAY: "自昨天起",
								SINCE_LAST_WEEK: "自上星期起",
								PAST_WEEKS: "在最近%d星期內",
								SINCE_LAST_MONTH: "自上個月起",
								PAST_MONTHS: "在過去%d個月內"
							},
							ms: {
								SOMEONE: "seseorang",
								RECENTLY: "Sejak kebelakangan",
								BY: "oleh",
								PAST_MINUTES: "dalam %d minit yang lalu",
								PAST_HOURS: "dalam %d jam yang lalu",
								PAST_DAYS: "dalam %d hari yang lalu",
								LAST_HOURS: "dalam %d jam terakhir",
								LAST_DAYS: "dalam %d hari terakhir",
								VERIFIED_BY: "disahkan oleh",
								VISITED_PAGE: "melayari halaman ini",
								ONLINE: "dalam talian",

								JUST_NOW: "sebentar tadi",
								PAST_HOUR: "dalam %d jam yang lalu",
								SINCE_YESTERDAY: "sejak semalam",
								SINCE_LAST_WEEK: "sejak minggu lalu",
								PAST_WEEKS: "dalam %d minggu terakhir",
								SINCE_LAST_MONTH: "sejak bulan lalu",
								PAST_MONTHS: "dalam %d bulan yang lalu"
							},
							et: {
								SOMEONE: "keegi",
								RECENTLY: "viimati",
								BY: "poolt",
								PAST_MINUTES: "viimase %d minuti jooksul",
								PAST_HOURS: "viimase %d tunni jooksul",
								PAST_DAYS: "viimase %d päeva jooksul",
								LAST_HOURS: "viimase %d tunni jooksul",
								LAST_DAYS: "viimase %d päeva jooksul",
								VERIFIED_BY: "poolt tõendatud",
								VISITED_PAGE: "külastas seda saiti",
								ONLINE: "võrgus",

								JUST_NOW: "just nüüd",
								PAST_HOUR: "viimase tunni jooksul",
								SINCE_YESTERDAY: "eilsest alates",
								SINCE_LAST_WEEK: "viimase nädala jooksul",
								PAST_WEEKS: "viimase %d nädala jooksul",
								SINCE_LAST_MONTH: "viimase kuu jooksul",
								PAST_MONTHS: "viimase %d kuu jooksul"
							}
						};

						var translated = langDict[code][key] || langDict['en'][key];
						if (value) {
							return translated.replace('%d', value);
						}
						return translated;
					}

					function getTimeDescription(t, settings) {
						if (settings && settings.hideExactTime) {
							return localizedString("RECENTLY");
						} else {
							var d = new Date() - t * 60 * 1000;
							switch (typeof d) {
								case 'number':
									break;
								case 'string':
									d = +new Date(d);
									break;
								case 'object':
									if (d.constructor == Date) d = d.getTime();
									break;
								default:
									d = +new Date();
							}
							var time_formats = [
								[60, 'seconds', 1], // 60
								[120, 'in the past minute', '1 minute from now'], // 60*2
								[3600, 'PAST_MINUTES', 60], // 60*60, 60
								[7200, 'PAST_HOUR', '1 hour from now'], // 60*60*2
								[86400, 'PAST_HOURS', 3600], // 60*60*24, 60*60
								[172800, 'SINCE_YESTERDAY', 'Tomorrow'], // 60*60*24*2
								[604800, 'PAST_DAYS', 86400], // 60*60*24*7, 60*60*24
								[1209600, 'SINCE_LAST_WEEK', 'Next week'], // 60*60*24*7*4*2
								[2419200, 'PAST_WEEKS', 604800], // 60*60*24*7*4, 60*60*24*7
								[4838400, 'SINCE_LAST_MONTH', 'Next month'], // 60*60*24*7*4*2
								[29030400, 'PAST_MONTHS', 2419200], // 60*60*24*7*4*12, 60*60*24*7*4
								[58060800, 'SINCE_LAST_YEAR', 'Next year'], // 60*60*24*7*4*12*2
								[2903040000, 'PAST_YEARS', 29030400], // 60*60*24*7*4*12*100, 60*60*24*7*4*12
								[5806080000, 'Last century', 'Next century'], // 60*60*24*7*4*12*100*2
								[58060800000, 'centuries', 2903040000] // 60*60*24*7*4*12*100*20, 60*60*24*7*4*12*100
							];
							var seconds = (+new Date() - d) / 1000,
								list_choice = 1;

							if (seconds <= 120) {
								return localizedString("JUST_NOW")
							}
							if (seconds < 0) {
								seconds = Math.abs(seconds);
								list_choice = 2;
							}
							var i = 0,
								format;
							while (format = time_formats[i++])
								if (seconds < format[0]) {
									if (typeof format[2] == 'string')
										return localizedString(format[list_choice]);
									else
										return localizedString(format[1], Math.floor(seconds / format[2]));
								}
							return d;
						}
					}

					function getPosition(n) {
						var position = n.settings.position ? n.settings.position.toLowerCase().split(" ").join("-") : 'bottom-left';
						var side = position.split('-')[1];
						var showOnTop = n.settings.hasOwnProperty("mobileTop") && n.settings.mobileTop;

						if (provesrc.utils.isMobile()) {
							if (side === 'center') side = 'right';
							if (showOnTop) {
								return 'top-' + side;
							}
							return 'bottom-' + side;
						}

						return position;
					}

					function isRTL(n) {
						return n.settings && n.settings.rtl ? 'rtl-bubble' : '';
					}

					function shouldBeVisible(n) {
						return n.settings && n.settings.allowClose ? 'is-visible' : 'no-visible';
					}

					function showNotificationCTA(params) {
						var link = provesrc.utils.get(params, 'settings.link') || {active: false};
						var cta = provesrc.utils.get(link, 'cta') || {active: false, text: '', color: ''};
						if (link && link.active && cta && cta.active) {
							return '<div class="bubble-cta" style="color:' + cta.color + '; !important">' + cta.text + ' <span class="cta-arrow arrow-bouncer" style="color:' + cta.color + '; !important"></span></div>';
						} else {
							return '';
						}
					}

					function showBranding(params) {

						function getBrandingText() {
							if (provesrc.settings.whitelabel && provesrc.settings.branding && provesrc.settings.branding.active && provesrc.settings.branding.text && provesrc.settings.branding.text.length > 0) {
								return " " + provesrc.settings.branding.text;
							} else {
								return " ProveSource";
							}
						}

						function getBranding(color) {
							// if(params.type == provesrc.constants.notificationTypes.COMBO) {
							// 	return localizedString("VERIFIED_BY")
							// } else {
							var bt = '<i><svg style="position: relative; top: 0.5px;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 224 224" width="24px" height="24px"><g fill="none" fill-rule="nonzero" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none" font-size="none" text-anchor="none" style="mix-blend-mode: normal"><path d="M0,224v-224h224v224z" fill="none"/><g><path d="M112,18.66667c-51.548,0 -93.33333,41.78533 -93.33333,93.33333c0,51.548 41.78533,93.33333 93.33333,93.33333c51.548,0 93.33333,-41.78533 93.33333,-93.33333c0,-51.548 -41.78533,-93.33333 -93.33333,-93.33333zM165.26533,90.59867l-65.33333,65.33333c-1.82,1.82 -4.20933,2.73467 -6.59867,2.73467c-2.38933,0 -4.77867,-0.91467 -6.59867,-2.73467l-28,-28c-3.64933,-3.64933 -3.64933,-9.548 0,-13.19733c3.64933,-3.64933 9.548,-3.64933 13.19733,0l21.40133,21.40133l58.73467,-58.73467c3.64933,-3.64933 9.548,-3.64933 13.19733,0c3.64933,3.64933 3.64933,9.548 0,13.19733z" fill="' + color + '"/></g></g></svg></i> ';
							// if(params.type != provesrc.constants.notificationTypes.INFORMATIONAL && params.type != provesrc.constants.notificationTypes.REVIEW) {
							// 	bt += localizedString("BY");
							// }
							return bt;
							// }
						}

						const bgColor = params.settings.theme && params.settings.theme.backgroundColor ? params.settings.theme.backgroundColor : '#fff';
						let brandingColor = getColorContrast(bgColor, 60, 80);
						if (!provesrc.settings.whitelabel || (provesrc.settings.whitelabel && provesrc.settings.branding && provesrc.settings.branding.active)) {
							if (!provesrc.settings.whitelabel || (provesrc.settings.whitelabel && provesrc.settings.branding.link && provesrc.settings.branding.link.length > 0)) {
								return '<div class="pfs-link" data-no-translation>' +
									'<span class="bubble-bolt">' + getBranding(brandingColor) + '</span>' +
									'<a href="' + provesrc.URL.generateBrandingLink(params) + '" target="_blank" style="color: ' + brandingColor + ';" onclick="event.stopPropagation()">' + getBrandingText() + '</a>' +
									'</div>'
							} else {
								return '<div class="pfs-link">' +
									'<span class="bubble-bolt">' + getBranding(bgColor) + '</span>' +
									'<a style="color: ' + brandingColor + '; "onclick="event.stopPropagation()">' + getBrandingText() + '</a>' +
									'</div>'
							}

						}
						return "";
					}

					function getComboPeriod(n) {
						switch (n.settings.combo.period) {
							case 'day':
							case 'week':
							case 'month':
								return localizedString("LAST_HOURS", n.settings.combo.period);
							case 'all':
								return getTimeDescription(n.minutes, n.settings);
								break;
						}
					}

					function getNotificationMessage(n) {
						if (n.type == provesrc.constants.notificationTypes.COMBO) {
							if (n.settings.combo.type == 'visits') {
								if (n.localization == 'tr') {
									return getComboPeriod(n) + " " + localizedString("VISITED_PAGE")
								} else {
									return localizedString("VISITED_PAGE") + " " + getComboPeriod(n)
								}
							} else {
								if (n.localization == 'tr') {
									return getComboPeriod(n) + " " + n.message;
								} else {
									return n.message + " " + getComboPeriod(n);
								}
							}
						} else {
							return n.message;
						}
					}

					function getStreamLocation(location, forceCountryName) {

						function generateLocationString(location, forceCountryName) {
							if (location) {
								if (location.city) {
									if (location.stateCode) return location.city + ', ' + location.stateCode;
									else if (location.countryCode) return location.city + ', ' + ((forceCountryName && location.country) ? location.country : location.countryCode);
									else if (location.country) return location.city + ', ' + location.country;
									else return location.city;
								} else if (location.state) {
									if (location.countryCode) return location.state + ', ' + location.countryCode;
									else return location.state;
								} else if (location.country) {
									return location.country;
								} else {
									return null;
								}
							}
						}

						var locationStr = generateLocationString(location, forceCountryName);
						return {
							locText: locationStr,
							locNode: locationStr ? " <span class='stream-location text-truncate'>(" + locationStr + ")</span> " : ""
						};
					}

					function getStreamName(data, params) {
						var leadName = null;
						if (data) {
							if (data.name == "Someone") {
								leadName = localizedString("SOMEONE");
							} else {
								leadName = data.name
							}
						} else {
							leadName = localizedString("SOMEONE");
						}

						if (params.settings.hideLocation) {
							return leadName;
						} else if (params.settings.showLocationFlag && data.location && data.location.countryCode) {
							return leadName + " <span class='stream-location text-truncate'><img alt='provesource country flag image' class='location-flag' src='https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/flags/" + data.location.countryCode.toLowerCase() + ".svg'></span>"
						} else {
							var fulltitle = null;
							var processedLocation = getStreamLocation(data.location, true);
							if (processedLocation.locText) {
								fulltitle = leadName + processedLocation.locText;
								if (fulltitle.length < 38) {
									return leadName + processedLocation.locNode;
								} else {
									return leadName + getStreamLocation(data.location).locNode;
								}
							} else {
								return leadName + processedLocation.locNode;
							}
						}
					}

					function getIconBackground(params) {
						if (params.data && params.data.product && params.data.product.image) {
							return '';
						} else if (params.data.initials && (!params.image || params.image == "")) {
							return 'bubble-icon-bg';
						} else if (!params.data.initials && (!params.image || params.image == "")) {
							return '';
						} else {
							return '';
						}
					}

					function getGlassUserIconSVG(bgColor, roundness = 0) {
						const color = getColorContrast(bgColor, 40, 80);
						var circleFill = provesrc.utils.hexToRgba(color, 0.3);
						var iconColor = provesrc.utils.hexToRgba(color, 0.50);
						var maxCSSRoundness = 35;
						var maxSVGRoundness = 22;
						var rx = Math.min((roundness / maxCSSRoundness) * maxSVGRoundness, maxSVGRoundness);
						return `
						<svg width="100%" height="50" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
						  <rect x="2" y="2" width="44" height="44" rx="${rx}" ry="${rx}" fill="${circleFill}" stroke-width="1"/>
						  <g>
							<circle cx="24" cy="22" r="6" fill="${iconColor}"/>
							<path d="M16 34c0-4.418 3.582-8 8-8s8 3.582 8 8" fill="${iconColor}"/>
						  </g>
						</svg>
						`;
					}

					function getReviewAuthorImage(params) {
						if (params.data && params.data.profilePhotoUrl) {
							return '<img style="border-radius: 35px; " alt="provesource social proof notification image" onerror="this.src=\'https://cdn.provesrc.com/avatar.svg\'" src="' + params.data.profilePhotoUrl + '">';
						} else if (params.data.initials) {
							const color = getColorContrast(params.settings.theme.backgroundColor, 40, 80);
							var backgroundColor = provesrc.utils.hexToRgba(color, 0.2);
							var initialsColor = provesrc.utils.hexToRgba(color, 0.6);
							const glassStyle = `
							border-radius: ${params.settings.rounded}px;
							background: ${backgroundColor};
							padding: 0 12px;
							color: ${initialsColor};`;
							return '<span class="bubble-initials" style="' + glassStyle + '">' + params.data.initials + '</span>';
						} else {
							return getGlassUserIconSVG(params.settings.theme.backgroundColor, params.settings.rounded);
						}
					}

					function getStreamImage(params) {
						if (params.data && params.data.hasOwnProperty("map")) {
							const imageBackground = provesrc.utils.getDarkerColor(params.settings.theme.backgroundColor, 10);
							var mapIcon = null;
							if (params.data.location && params.data.map) {
								mapIcon = params.data.map;
							} else {
								mapIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/map-placeholder.png";
							}
							return `
						<div style="position: relative; display: inline-block; width: 50px; height: 50px;">
						  <img alt="provesource social proof notification image"
							   onerror="this.src='https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/map-placeholder.png'"
							   src="${mapIcon}"
							   style="display: block; width: 100%; height: 100%;">
						  <div style="
							position: absolute;
							top: 0; left: 0; right: 0; bottom: 0;
							background: ${imageBackground};
							opacity: 0.15;
							pointer-events: none;
							border-radius: ${params.settings.rounded * 2}px;
						  "></div>
						</div>
					  `;
						} else if (params.data && params.data.product && params.data.product.image) {
							var imageOnError = (params.image && params.image.length) ? params.image : "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/no-image.png";
							return '<img alt="provesource social proof notification image" onerror="this.src=' + "'" + imageOnError + "'" + '" src="' + params.data.product.image + '">';
						} else if (params.image && params.image.length) {
							return '<img alt="provesource social proof notification image" onerror="this.src=\'https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/no-image.png\'" src=' + "\"" + params.image + "\">";
						} else if (params.data.initials) {
                            let color = getColorContrast(params.settings.theme.backgroundColor, 40, 80);
                            var backgroundColor = provesrc.utils.hexToRgba(color, 0.2);
							var initialsColor = provesrc.utils.hexToRgba(color, 0.6);
							const glassStyle = `
							border-radius: ${params.settings.rounded}px;
							background: ${backgroundColor};
							padding: 0 12px;
							color: ${initialsColor};`;
							return '<span class="bubble-initials" style="' + glassStyle + '">' + params.data.initials + '</span>';
						} else {
							return getGlassUserIconSVG(params.settings.theme.backgroundColor, params.settings.rounded);
						}
					}

					function getStreamTime(params) {
						var hideExactTimeStream = provesrc.utils.get(params, 'settings.hideExactTimeStream', {
							active: false,
							unit: 'Hours',
							value: ''
						});
						if (hideExactTimeStream && hideExactTimeStream.active) {
							var dateNow = new Date();
							var notificationTime = new Date(params.timestamp);
							switch (hideExactTimeStream.unit) {
								case "Hours":
									return dateNow.getTime() > (notificationTime.setHours(notificationTime.getHours() + hideExactTimeStream.value)) ? localizedString("RECENTLY") : params.data.timeText;
									break;
								case "Minutes":
									return dateNow.getTime() > (notificationTime.setMinutes(notificationTime.getMinutes() + hideExactTimeStream.value)) ? localizedString("RECENTLY") : params.data.timeText;
									break;
							}
						} else {
							return params.data.timeText
						}
					}

					function getNotificationTitle(params) {
						if (params && params.title && params.title.length > 0 && params.title != "") {
							return '<div class="ps-bubble-title text-truncate">' +
								'<span>' + params.title + '</span>' +
								'</div>'
						}
						return "";
					}

					function shouldCapitalize(data) {
						return data && data.initials ? 'text-capitalize' : '';
					}

					function shouldOpenLinkInNewTab(link) {
						return link && link.active && link.newTab ? "target='_blank' " : "";
					}

					function shouldShowProduct(n) {
						if (n.data && n.data.product && !n.settings.hideProduct) {
							var product = n.data.product;
							if (n.settings.disableProductLink || provesrc.settings.forceNoProductLinks || !product.link) {
								return '<span id="ps-stream-no-product-link">' + product.name + '</span>';
							} else {
								return '<a id="ps-stream-product-link" class="bubble-product-link" ' + shouldOpenLinkInNewTab(product.link) + ' href="' + (product.link && (product.link + provesrc.URL.generateLinkUTMParams(n, product.link))) + '">' + product.name + '</a>';
							}
						}
						return "";
					}

					function getReviewSourceIcon(params) {
						var sourceIcon = null;
						var sourceName = null;
						switch (params.data.source) {
							case "google":
								sourceName = 'Google';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/google-review-icon.png";
								break;
							case "other":
							case "trustpilot":
								sourceName = '';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/trustpilot-review-icon.png";
								break;
							case "yotpo":
								sourceName = 'Yotpo';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/yotpo-review-icon.png";
								break;
							case "stamped":
								sourceName = 'Stamped';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/stamped-review-icon.png";
								break;
							case "facebook":
								sourceName = 'Facebook';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/facebook-review-icon.png";
								break;
							case "reviewsio":
								sourceName = 'Reviews.io';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/reviewsio-review-icon.png";
								break;
							case "capterra":
								sourceName = 'Capterra';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/capterra-review-icon.png";
								break;
							case "judgeme":
								sourceName = 'Judge.me';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/judgeme-review-icon.png";
								break;
							case "feefo":
								sourceName = 'Feefo';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/feefo-review-icon.png";
								break;
							case "shopperapproved":
								sourceName = 'Shopper Approved';
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/shopper-approved-review-icon.png";
								break;
							case "shapo":
								sourceName = 'Shapo';
								sourceIcon = "https://cdn.shapo.io/assets/icons/shapo.svg";
								break;
							case "wix":
								sourceName = 'Wix';
								sourceIcon = "https://cdn.shapo.io/assets/icons/wix.svg";
								break;
							default:
								break;
						}
						return '<div class="review-source-container"><img class="_ps-review-source" alt="provesource review source" src="' + sourceIcon + '"><span class="review-source-name">' + sourceName + '</span></div>';					}

					function decodeHTMLReview(text) {
						var txter = document.createElement("textarea");
						txter.innerHTML = text.replaceAll('&amp;#039;', '\'');
						return txter.value;
					}

					function getReviewText(data) {
						if (data.text && data.text.length > 0) {
							return "\"" + decodeHTMLReview(data.text) + "\"";
						}
						return "";
					}

					function getSocialIcon(source) {
						var sourceIcon = null;
						switch (source) {
							case provesrc.constants.socialNetworks.FACEBOOK :
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/facebook-icon.png";
								break;
							case provesrc.constants.socialNetworks.TWITTER :
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/x-icon.png";
								break;
							case provesrc.constants.socialNetworks.YOUTUBE :
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/youtube-icon.png";
								break;
							case provesrc.constants.socialNetworks.INSTAGRAM :
								sourceIcon = "https://cdn-provesrc.nyc3.cdn.digitaloceanspaces.com/assets/instagram-icon.png";
								break;
							default:
								break;
						}
						return '<img src="' + sourceIcon + '" alt="' + source + 'logo">';
					}

					function socialProfileRedirect(socialNetwork, profileName) {
						var profileUrl = null;
						switch (socialNetwork) {
							case provesrc.constants.socialNetworks.FACEBOOK :
								if (profileName.indexOf("facebook.com") > -1) {
									profileUrl = profileName;
								} else {
									profileUrl = 'https://www.facebook.com/' + profileName;
								}
								break;
							case provesrc.constants.socialNetworks.TWITTER :
								profileUrl = 'https://twitter.com/' + profileName;
								break;
							case provesrc.constants.socialNetworks.YOUTUBE :
								profileUrl = 'https://www.youtube.com/channel/' + profileName;
								break;
							case provesrc.constants.socialNetworks.INSTAGRAM :
								profileUrl = 'https://www.instagram.com/' + profileName;
								break;
							default:
								break;
						}
						return profileUrl;
					}

					function getSocialDescription(profiles) {
						var socialItems = '';
						if (profiles) {
							for (var property in profiles) {
								if (profiles[property].hasOwnProperty('value') && profiles[property].value.length > 0) {
									switch (property) {
										case provesrc.constants.socialNetworks.FACEBOOK:
										case provesrc.constants.socialNetworks.TWITTER:
										case provesrc.constants.socialNetworks.YOUTUBE:
										case provesrc.constants.socialNetworks.INSTAGRAM:
											socialItems += '<div class="social-item" onclick="window.open(\'' + socialProfileRedirect(property, profiles[property].id) + '\')">' +
												getSocialIcon(property) +
												'<span>' + profiles[property].value + '</span>' +
												// '<p>Likes</p>' +
												'</div>';
											break;
										default:
											break;
									}
								}
							}
						}
						return socialItems;
					}

					function getSocialMessage(params) {
						if (params && params.message && params.message.length > 0 && params.message != "") {
							return '<div class="social-text">' +
								'<span>' + window.snarkdown(params.message) + '</span>' +
								'</div>'
						}
						return "";
					}

					if (params.type == provesrc.constants.notificationTypes.LIVE) {

						return '<div id="provesrc-notification-container" class="ps-animated ps-hide ' + getPosition(params) + '">' +
							'<div id="provesrc-bubble-body-container" class="bubble-body live-type mx-auto no-text-select ' + isRTL(params) + '" ' +
							'onmouseenter="provesrc.HTML.manageMouseHover(true, true)" onmouseleave="provesrc.HTML.manageMouseHover()">' +
							'<div class="bubble-icon"><svg class="ps-pulse-dot" expanded="true"><circle cx="50%" cy="50%" r="14px"></circle><circle class="_ps-pulse" cx="50%" cy="50%" r="14px"></circle></svg></div>' +
							'<div class="bubble-content">' +
							'<div class="ps-bubble-title text-truncate">' +
							'<span>' + numberFormatter(params) + '</span>' +
							'</div>' +
							'<div class="bubble-description">' +
							'<span>' + params.refer + ' ' + localizedString("ONLINE") + '</span> ' +
							'</div>' +
							'<div class="bubble-time">' +
							'<div>' + showBranding(params) + '</div></div>' +
							'<span id="ps-bubble-close" class="' + shouldBeVisible(params) + '" onclick="provesrc.VISIBILITY._psCloseNotification(\'' + params._id + '\', event)">' +
							'<span class="close-before"></span>' +
							'<span class="close-after"></span>' +
							'</span>' +
							'</div></div></div>';

					} else if (params.type == provesrc.constants.notificationTypes.STREAM) {

						return '<div id="provesrc-notification-container" class="ps-animated ps-hide ' + getPosition(params) + '">' +
							'<div id="provesrc-bubble-body-container" class="bubble-body stream-type mx-auto no-text-select ' + isRTL(params) + '" ' +
							'onmouseenter="provesrc.HTML.manageMouseHover(true, true)" onmouseleave="provesrc.HTML.manageMouseHover()">' +
							'<div class="bubble-content">' +
							'<div class="bubble-icon ' + getIconBackground(params) + '">' + getStreamImage(params) + '</div>' +
							'<div class="bubble-content-inner">' +
							'<div class="ps-bubble-title text-truncate">' +
							'<span class="' + shouldCapitalize(params.data) + '">' + getStreamName(params.data, params) + '</span>' +
							'</div>' +
							'<div class="bubble-description">' +
							'<span>' + window.snarkdown(params.message) + '</span> ' + shouldShowProduct(params) +
							'</div>' +
							'<div class="bubble-time">' +
							(params.settings.showDate
									? '<span>' + getStreamTime(params) + '</span>'
									: ''
							) +
							showBranding(params) +
							'</div>' +
							'</div></div>' + showNotificationCTA(params) +
							'<span id="ps-bubble-close" class="' + shouldBeVisible(params) + '" onclick="provesrc.VISIBILITY._psCloseNotification(\'' + params._id + '\', event)">' +
							'<span class="close-before"></span>' +
							'<span class="close-after"></span>' +
							'</span>' +
							'</div>' +
							'</div></div></div>';


					} else if (params.type == provesrc.constants.notificationTypes.COMBO) {

						return '<div id="provesrc-notification-container" class="ps-animated ps-hide ' + getPosition(params) + '">' +
							'<div id="provesrc-bubble-body-container" class="bubble-body combo-type mx-auto no-text-select ' + isRTL(params) + '" ' +
							'onmouseenter="provesrc.HTML.manageMouseHover(true, true)" onmouseleave="provesrc.HTML.manageMouseHover()">' +
							'<div class="bubble-icon">' +
							'<div class="combo-number">' +
							'<div class="num-value scale">' + numberFormatter(params) + '</div>' +
							'<div class="refer text-truncate">' + params.refer + '</div> ' +
							'</div>' +
							'</div>' +
							'<div class="bubble-content' + (provesrc.settings.whitelabel ? ' no-branding' : '') + '">' +
							'<div class="bubble-description">' +
							'<span>' + getNotificationMessage(params) + '</span>' +
							'</div>' +
							'<div class="bubble-time">' +
							'<div>' + showBranding(params) + '</div>' +
							'</div>' +
							'<span id="ps-bubble-close" class="' + shouldBeVisible(params) + '" onclick="provesrc.VISIBILITY._psCloseNotification(\'' + params._id + '\', event)">' +
							'<span class="close-before"></span>' +
							'<span class="close-after"></span>' +
							'</span>' +
							'</div></div></div>';

					} else if (params.type == provesrc.constants.notificationTypes.INFORMATIONAL) {
						return '<div id="provesrc-notification-container" class="ps-animated ps-hide ' + getPosition(params) + '">' +
							'<div id="provesrc-bubble-body-container" class="bubble-body informational-type mx-auto no-text-select ' + isRTL(params) + '" ' +
							'onmouseenter="provesrc.HTML.manageMouseHover(true, true)" onmouseleave="provesrc.HTML.manageMouseHover()">' +
							'<div class="bubble-icon">' +
							'<img alt="provesource social proof notification image" src=' + "\"" + params.image + "\">" +
							'</div>' +
							'<div class="bubble-content">' +
							getNotificationTitle(params) +
							'<div class="bubble-description">' +
							'<span>' + window.snarkdown(params.message) + '</span>' +
							'</div>' +
							'<div class="bubble-time"><div>' + showBranding(params) +
							'</div></div>' + showNotificationCTA(params) +
							'<span id="ps-bubble-close" class="' + shouldBeVisible(params) + '" onclick="provesrc.VISIBILITY._psCloseNotification(\'' + params._id + '\', event)">' +
							'<span class="close-before"></span>' +
							'<span class="close-after"></span>' +
							'</span>' +
							'</div></div></div>';

					} else if (params.type == provesrc.constants.notificationTypes.REVIEW) {
						return '<div id="provesrc-notification-container" class="ps-animated ps-hide ' + getPosition(params) + '">' +
							'<div id="provesrc-bubble-body-container" class="bubble-body review-type mx-auto no-text-select ' + isRTL(params) + '" ' +
							'onmouseenter="provesrc.HTML.manageMouseHover(true, true)" onmouseleave="provesrc.HTML.manageMouseHover()">' +
							'<div class="bubble-icon">' + getReviewAuthorImage(params) + '</div>' +
							'<div class="bubble-content"><div class="ps-bubble-title">' +
							'<span>' + ((params.data.authorName && params.data.authorName.length > 0) ? params.data.authorName : localizedString("SOMEONE")) + '</span>' +
							'</div>' +
							'<div class="bubble-description" style="color: ' + params.settings.theme.messageColor + ';">' +
							'<span>' + getReviewText(params.data) + '</span>' +
							'</div>' +
							'<div class="bubble-time"><div><div class="_ps-review-rating" >' +
							getReviewSourceIcon(params) +
							'<span class="_ps-review-rating-star"><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="#ffc100" viewBox="0 0 24 24"><path d="M12 .587l3.668 7.568 8.332 1.151-6.064 5.828 1.48 8.279-7.416-3.967-7.417 3.967 1.481-8.279-6.064-5.828 8.332-1.151z"/></svg></span>' +
							'<span class="_ps-review-rating-star"><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="#ffc100" viewBox="0 0 24 24"><path d="M12 .587l3.668 7.568 8.332 1.151-6.064 5.828 1.48 8.279-7.416-3.967-7.417 3.967 1.481-8.279-6.064-5.828 8.332-1.151z"/></svg></span>' +
							'<span class="_ps-review-rating-star"><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="#ffc100" viewBox="0 0 24 24"><path d="M12 .587l3.668 7.568 8.332 1.151-6.064 5.828 1.48 8.279-7.416-3.967-7.417 3.967 1.481-8.279-6.064-5.828 8.332-1.151z"/></svg></span>' +
							'<span class="_ps-review-rating-star"><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="#ffc100" viewBox="0 0 24 24"><path d="M12 .587l3.668 7.568 8.332 1.151-6.064 5.828 1.48 8.279-7.416-3.967-7.417 3.967 1.481-8.279-6.064-5.828 8.332-1.151z"/></svg></span>' +
							'<span class="_ps-review-rating-star"><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="#ffc100" viewBox="0 0 24 24"><path d="M12 .587l3.668 7.568 8.332 1.151-6.064 5.828 1.48 8.279-7.416-3.967-7.417 3.967 1.481-8.279-6.064-5.828 8.332-1.151z"/></svg></span>' +
							'</div>' + showBranding(params) +
							'</div></div>' + showNotificationCTA(params) +
							'<span id="ps-bubble-close" class="' + shouldBeVisible(params) + '" onclick="provesrc.VISIBILITY._psCloseNotification(\'' + params._id + '\', event)">' +
							'<span class="close-before"></span>' +
							'<span class="close-after"></span>' +
							'</span>' +
							'</div></div></div>';

					} else if (params.type == provesrc.constants.notificationTypes.SOCIAL) {
						return '<div id="provesrc-notification-container" class="ps-animated ps-hide ' + getPosition(params) + '">' +
							'<div id="provesrc-bubble-body-container" class="bubble-body social-type mx-auto no-text-select ' + isRTL(params) + '" ' +
							'onmouseenter="provesrc.HTML.manageMouseHover(true, true)" onmouseleave="provesrc.HTML.manageMouseHover()">' +
							getSocialMessage(params) +
							'<div class="bubble-description" style="color: ' + params.settings.theme.titleColor + ';">' +
							getSocialDescription(params.profiles) +
							'</div>' +
							'<div class="bubble-time" style="margin-bottom: 5px"><div>' + showBranding(params) +
							'</div></div>' + showNotificationCTA(params) +
							'<span id="ps-bubble-close" class="' + shouldBeVisible(params) + '" onclick="provesrc.VISIBILITY._psCloseNotification(\'' + params._id + '\', event)">' +
							'<span class="close-before"></span>' +
							'<span class="close-after"></span>' +
							'</span>' +
							'</div></div>'

					} else {

						return '<div id="provesrc-notification-container" class="ps-animated ps-hide ' + getPosition(params) + '">' +
							'<div id="provesrc-bubble-body-container" class="bubble-body mx-auto no-text-select ' + isRTL(params) + '" ' +
							'onmouseenter="provesrc.HTML.manageMouseHover(true, true)" onmouseleave="provesrc.HTML.manageMouseHover()">' +
							'<div class="bubble-icon">' +
							'<img alt="provesource social proof notification image" src=' + "\"" + params.image + "\">" +
							'</div>' +
							'<div class="bubble-content">' +
							'<div class="ps-bubble-title text-truncate">' +
							'<span>' + numberFormatter(params) + ' ' + params.refer + '</span>' +
							'</div>' +
							'<div class="bubble-description">' +
							'<span>' + window.snarkdown(params.message) + '</span>' +
							'</div>' +
							'<div class="bubble-time">' +
							'<div><span>' + getTimeDescription(params.minutes, params.settings) + ' ' +
							'</span>' + showBranding(params) +
							'</div></div>' + showNotificationCTA(params) +
							'<span id="ps-bubble-close" class="' + shouldBeVisible(params) + '" onclick="provesrc.VISIBILITY._psCloseNotification(\'' + params._id + '\', event)">' +
							'<span class="close-before"></span>' +
							'<span class="close-after"></span>' +
							'</span>' +
							'</div></div></div>';

					}

				}

				,
				setNotificationVisibility: function (shouldDisplay) {
					var psContainer = document.getElementById(provesrc.constants.psNotificationContainerId);
					if (psContainer && _psThisNotification) {
						var showAnimation = null;
						var closeAnimation = null;
						if (provesrc.utils.isMobile()) {
							psContainer.classList.remove("_ps-desktop");
							psContainer.classList.remove("_ps-mobile-small");
							if (_psThisNotification.settings.mobileDesign) {
								if (_psThisNotification.settings.mobileDesign === 'small') {
									psContainer.classList.add("_ps-mobile-small");
								}
							}
							if (_psThisNotification.settings.mobileTop) {
								showAnimation = provesrc.settings.animations.top.show;
								closeAnimation = provesrc.settings.animations.top.close;
							} else {
								showAnimation = provesrc.settings.animations.bottom.show;
								closeAnimation = provesrc.settings.animations.bottom.close;
							}
						} else {
							psContainer.classList.add("_ps-desktop");
							psContainer.classList.remove("_ps-mobile-small");
							switch (_psThisNotification.settings.position) {
								case "Bottom Left":
								case "Bottom Right":
								case "Bottom Center":
									showAnimation = provesrc.settings.animations.bottom.show;
									closeAnimation = provesrc.settings.animations.bottom.close;
									break;
								case "Top Left":
								case "Top Right":
									showAnimation = provesrc.settings.animations.top.show;
									closeAnimation = provesrc.settings.animations.top.close;
									break;
							}
						}


						if (shouldDisplay) {
							provesrc.debug("[Notifications Status]: Showing notification");
							_psIsShowingNotification = true;
							psContainer.classList.remove('ps-hide');
							psContainer.classList.add('ps-show');
							psContainer.classList.add(showAnimation);
						} else {
							_psIsShowingNotification = false;
							provesrc.debug("[Notifications Status]: NOT showing notification");
							psContainer.classList.remove(showAnimation);
							psContainer.classList.add(closeAnimation);
						}
					}
				}
				,
				manageMouseHover: function (isEnter, trackHover) {
					if ((_psThisNotification || !provesrc.Q.isEmpty()) && !provesrc.utils.isMobile()) {
						if (isEnter) {
							provesrc.debug("Stopping timers.");
							if (trackHover) {
								provesrc.ANALYTICS.track(provesrc.constants.analyticsEvents.hover, _psThisNotification._id, _psThisNotification);
							}
							clearTimeout(_psCloseTimeoutTimer);
							clearTimeout(_psShowTimeoutTimer);
						} else {
							provesrc.debug("Resuming notification timers.");
							provesrc.show(null, true);
						}
					}
				}
			},
			//-------Remarketing
			DEBUG_MODE: {
				start: function () {
					var debugModeSign = "<div class='debug-mode-container'><div class='provesrc-logo'><img alt='provesource debug image' src='https://s3.amazonaws.com/proofsource-sdks/ps-icon+transparent-128.png'></div>" +
						"<div class='installed-note'><span class='green-dot'></span>Installed!</div>" +
						"<div><span class='admin-note'><span class='lock-icon'></span>Only you can see this</span></div></div>";

					var newDiv = document.createElement('div');
					newDiv.id = "provesrc-debug-mode";
					newDiv.innerHTML = debugModeSign;
					if (document && document.body) {
						document.body.appendChild(newDiv);
					}
				}
			},
			REMARKETING: {
				init: function (remarketingObj) {
					var hideRemarketing = provesrc.storage.get("hide-remarketing");
					var hidePlanLimit = provesrc.storage.get("hide-planlimit-remarketing");
					if (remarketingObj) {
						if (remarketingObj.planLimit) {
							if (hidePlanLimit) {
								provesrc.debug("[REMARKETING] Plan Limit is hidden...");

								var currentTime = new Date().getTime();
								if (currentTime < hidePlanLimit) return;
							}
							provesrc.debug("[REMARKETING] Starting Plan Limit remarketing...");
							provesrc.HTML.injectContainer('<div id="provesrc-notification-container" class="ps-animated ps-show bottom-left">' +
								'<div id="provesrc-bubble-body-container" class="bubble-body plan-limit-reached" style="padding: 1rem; width: 340px; cursor: pointer; border-radius: 13px">' +
								'<div class="box-title" style="font-size: 14px !important;  font-weight: 600;border-radius: 6px; background: #f5f5f5;"><span><span class="lock-icon"></span>Only you can see this message</span></div>' +
								'<img src="https://provesrc.com/wp-content/uploads/2020/08/logo-transparent-text.svg" style="width: 170px; display: flex;margin-top: 8px;"> ' +
								'<div class="limit-title" style="line-height: 1.3;font-weight: 600;">ProveSource is <span style="color: #e5040d;text-decoration: underline">inactive</span> because you have reached your monthly limit.</div>' +
								'<div class="limit-content" style="color: #2d2d2d;"><strong>' + numberFormatter(null, remarketingObj.planLimit.passed) + '</strong> visitors have already missed your ProveSource notifications!' +
								'<div class="limit-cta" style="padding-top: 10px;"><a class="box-cta" style="width: 100%; background: #e5040d; border-color: #e70009;">Upgrade now to reactivate</a></div>' +
								'<span id="ps-bubble-close" style="right: 5px; top:5px;" onclick="provesrc.VISIBILITY._psCloseNotification(\'planlimit\', event)">' +
								'<span class="close-before"></span>' +
								'<span class="close-after"></span>' +
								'</span></div></div>', function () {
								setupClickListener(true);
								provesrc.debug("[REMARKETING] Injected plan limit notice")
							});

						} else if (remarketingObj.wizard && !hideRemarketing) {

							provesrc.debug("[REMARKETING] Starting ProveSource remarketing...");

							provesrc.HTML.injectContainer('<div id="provesrc-notification-container" class="ps-animated ps-show bottom-left"></div>', function () {
								injectRemarketingContainer();
							});

						}

						function injectRemarketingContainer() {
							var containerNode = document.getElementById("provesrc-notification-container");
							provesrc.debug("[REMARKETING] Injected remarketing popup");

							var message = "<strong>Wow, you rock!</strong><br>ProveSource is now installed   👋";
							var remarketingContainer = document.createElement('div');
							remarketingContainer.id = "provesrc-remarketing-container";
							remarketingContainer.class = "ps-animated psFadeInUp";
							remarketingContainer.innerHTML = '<div>' +
								'<div class="box-title "><span><span class="lock-icon"></span>Only you can see this.</span></div>' +
								'<div class="box-content">' + message + '</div>' +
								'<div><a class="box-cta">Create Notification</a></div>' +
								'</div>' +
								'<span id="ps-bubble-close" class="is-visible" onclick="provesrc.VISIBILITY._psCloseNotification(\'remarketing\', event)">' +
								'<span class="close-before"></span>' +
								'<span class="close-after"></span>' +
								'</span>';
							if (containerNode) {
								containerNode.appendChild(remarketingContainer);
								setupClickListener(null);
							}
						}

						function setupClickListener(isPlanLimit) {
							if (isPlanLimit) {
								var planLimitContainer = document.getElementById("provesrc-notification-container");
								if (planLimitContainer) {
									planLimitContainer.onclick = function (ev) {
										var linkOpen = provesrc.constants.planLimitRemarketingLink;
										window.open(linkOpen);
										ev.stopPropagation();
									};
								}
							} else {
								var remarketingContainer = document.getElementById("provesrc-remarketing-container");
								if (remarketingContainer) {
									remarketingContainer.onclick = function (ev) {
										window.location.href = "https://console.provesrc.com/#/notifications/new";
										ev.stopPropagation();
									};
								}
							}
						}

					}
				}
			},
			GOALS: {
				trackGoalEvent: function (event, notificationId) {
					var goalsObject = provesrc.storage.cookies.get(provesrc.constants.storageGoalsTrackingKey);

					if (!goalsObject) {
						goalsObject = {};
					} else {
						goalsObject = JSON.parse(goalsObject);
					}

					var notificationIds = Object.keys(goalsObject);
					for (var i = 0; i < notificationIds.length; i++) {
						if (goalsObject[notificationIds[i]] && goalsObject[notificationIds[i]].expires < provesrc.utils.currentTime()) {
							delete goalsObject[notificationIds[i]];
						}
					}

					if (!goalsObject[notificationId]) {
						goalsObject[notificationId] = {
							expires: provesrc.utils.getExpiryTime(provesrc.constants.analyticsUniqueEventTime),
							view: false,
							click: false,
							hover: false
						};
					}

					goalsObject[notificationId][event] = true;
					provesrc.storage.cookies.set(provesrc.constants.storageGoalsTrackingKey, JSON.stringify(goalsObject), false, "/", provesrc.URL.getRootDomain(), true);
					provesrc.debug("Tracked Goal Event:", event, "For notification:", notificationId);
				},
				prepareGoals: function () {
					var goalsObject = provesrc.storage.cookies.get(provesrc.constants.storageGoalsTrackingKey);
					if (!goalsObject) {
						return [];
					}

					goalsObject = JSON.parse(goalsObject);
					var notificationIds = Object.keys(goalsObject);
					var transformedGoals = [];
					for (var i = 0; i < notificationIds.length; i++) {
						var notificationId = notificationIds[i];
						var goal = goalsObject[notificationId];
						delete goal.expires;
						goal.notificationId = notificationId;
						transformedGoals.push(goal)
					}

					return transformedGoals;

				},
				reportGoals: function (goalId) {
					var goals = this.prepareGoals();
					var reqBody = {url: provesrc.URL.getCurrentURL(), events: goals};

					if ((goals && provesrc.settings.shouldSendGoals) || goalId) {
						reqBody = provesrc.utils.setSegmentUserId(reqBody);
						if (goalId) {
							reqBody.id = goalId;
						}

						provesrc.debug("[!] Reporting goals data:", goals, (goalId ? "for Goal ID " + goalId : "for URL: " + provesrc.URL.getCurrentURL()));
						provesrc.API.req(provesrc.API.endpoints.goals, 'POST', reqBody, function (err, res, data) {
							if (!err) {
								provesrc.debug("[√√√] -- Reported goals:", goals, (goalId ? "for Goal ID " + goalId : "for URL: " + provesrc.URL.getCurrentURL()));
							}
						});
					}
				}
			},
			ANALYTICS: {
				externalTrackers: {
					forwardEvent: function (event, notification) {
						provesrc.debug("[Analytics externalTrackers forwardEvent] Forwarding event --", event, "-- to trackers");
						var providers = Object.keys(provesrc.settings.trackers);
						for (var i = 0; i < providers.length; i++) {
							var provider = providers[i];
							var providerEnabled = provesrc.settings.trackers[provider];
							if (providerEnabled === true) {
								this.reportToExternalProvider(provider, event, notification)
							}
						}
					},
					reportToExternalProvider: function (provider, event, notification) {
						provesrc.debug("[Analytics externalTrackers reportToExternalProvider]:", provider, event, notification);

						//GTM DataLayer
						if (window.dataLayer && window.dataLayer.push) {
							window.dataLayer.push({'event': 'provesource-' + event, 'notification': notification.name});
						}
						//Other Providers
						switch (provider) {

							//--AMPLITUDE--//
							case provesrc.constants.analyticsProviders.Amplitude: {
								try {
									if (window.amplitude) {
										amplitude.getInstance().logEvent(event + " (ProveSource)", {
											from: "ProveSource",
											event: event,
											notification: notification.name
										});
										provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Sent Amplitude event:", event, "for notification:", notification.name)
									} else {
										provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Amplitude was not found")
									}
								} catch (e) {
									provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Failed to send event to Amplitude", e)
								}
								break;
							}

							//--GOOGLE ANALYTICS--//
							case provesrc.constants.analyticsProviders.GoogleAnalytics: {
								try {
									if (window.gtag) {
										var eventParams = {
											event_category: "ProveSource",
											event_label: getEventLabel(notification.name),
											non_interaction: true
										};
										if (event == 'click' && !provesrc.settings.GANonInteraction) {
											eventParams.non_interaction = false;
										}
										window.gtag("event", 'provesource_' + event, eventParams);
										provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Reported to Google Tag Manager")
									} else {
										provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Google Tag Manager was not found")
									}
								} catch (e) {
									provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Failed to send event to Google Tag Manager (window.gtag)", e)
								}

								try {
									if (window.ga || window[window['GoogleAnalyticsObject']]) {
										var interaction = {nonInteraction: true};
										if (event == 'click' && !provesrc.settings.GANonInteraction) {
											interaction = {nonInteraction: false};
										}

										var selectedTracker = 'Basic Tracker';
										var gaTracker = window.ga || window[window['GoogleAnalyticsObject']];
										var customTracker = gaTracker;
										var trackerKey = provesrc.settings.trackers.gaKey;
										if (gaTracker) {
											provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Found GA on the page")
											customTracker = trackerKey ? gaTracker.getByName(trackerKey) : null;
											if (customTracker) {
												selectedTracker = trackerKey;
												provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Customer Tracker:", customTracker, trackerKey);
												customTracker.send("event", 'provesource_' + event, 'provesource_' + event, notification.name, interaction);
											}
											if (gaTracker.getAll() && gaTracker.getAll().length > 0) {
												for (var x = 0; x < gaTracker.getAll().length; x++) {
													var specificTracker = gaTracker.getAll()[x];
													try {
														specificTracker.send("event", 'provesource_' + event, 'provesource_' + event, notification.name, interaction);
														provesrc.debug("[Analytics externalTrackers gaTracker]: Sent event to tracker", specificTracker);
													} catch (err) {
														provesrc.debug("[Analytics externalTrackers gaTracker]: ERROR sending event to tracker", specificTracker);
													}
												}
											} else {
												gaTracker("send", "event", 'provesource_' + event, 'provesource_' + event, notification.name, interaction);
											}
										}
										provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Reported to Google Analytics (Tracker: " + selectedTracker + ")")
									} else {
										provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Google Analytics was not found")
									}
								} catch (e) {
									provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Failed to send event to Google Analytics (window.ga)", e)
								}

								//-- GTM --//
								try {
									var gtmSent = false;
									if (typeof (window.google_tag_manager) != "undefined") {
										for (var dataLayerName in window.google_tag_manager) {
											if (window.google_tag_manager[dataLayerName].gtmDom == true) {
												var dataLayerParams = {
													event: 'provesource_' + event,
													event_name: 'provesource_' + event,
													event_action: 'provesource_' + event,
													notification: notification.name,
													action: 'provesource_' + event,
													'event_label': getEventLabel(notification.name),
													'non_interaction': true
												};
												window[dataLayerName].push(dataLayerParams);
												gtmSent = true;
												provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Pushed GTM DataLayer event", dataLayerParams);
											}
										}
									}
									if (!gtmSent) {
										provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: GTM DataLayer not found");
									}
								} catch (e) {
									provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Failed to send event to Google Tag Manager (window.google_tag_manager)", e)
								}

								break;
							}

							//--MIXPANEL--//
							case provesrc.constants.analyticsProviders.Mixpanel: {
								try {
									if (window.mixpanel) {
										mixpanel.track(event + " (ProveSource)", {
											from: "ProveSource",
											notification: notification.name
										});
										provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Sent mixpanel event:", event, "for notification:", notification.name)
									} else {
										provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: mixpanel was not found")
									}
								} catch (e) {
									provesrc.debug("[Analytics externalTrackers reportToExternalProvider]: Failed to send event to mixpanel", e)
								}
								break;
							}

							default:
								break;
						}

						function getEventLabel(event) {
							return event + " | " + provesrc.URL.getHostname();
						}
					}
				},
				conversionEvents: {
					report: function (event) {
						var conversionsObj = provesrc.storage.get(provesrc.constants.storageConversionAnalyticsKey, true);
						if (!conversionsObj) {
							var cObj = {viewed: null, clicked: null, hovered: null};
							provesrc.storage.set(provesrc.constants.storageConversionAnalyticsKey, cObj, true);
							conversionsObj = provesrc.storage.get(provesrc.constants.storageConversionAnalyticsKey, true);
						}
						switch (event) {
							case "hover":
								setEvent("hovered");
								break;
							case "view":
								setEvent("viewed");
								break;
							case "click":
								setEvent("clicked");
								break;
						}

						function setEvent(name) {
							var currentTime = new Date().getTime();
							var eventRef = conversionsObj[name];
							if (!eventRef || eventRef.t < currentTime) {
								conversionsObj[name] = {
									t: generateTimestamp(),
									r: true
								};
								provesrc.debug("Settings conversion event to report:", "|" + name + "|", conversionsObj[name]);
								provesrc.storage.set(provesrc.constants.storageConversionAnalyticsKey, conversionsObj, true);
							}
						}

						function generateTimestamp() {
							var uniqueEvent = new Date();
							uniqueEvent.setDate(uniqueEvent.getDate() + provesrc.constants.analyticsConversionActionEventTime);
							return uniqueEvent.getTime();
						}
					},
					getEvents: function () {
						var returnObj = {};
						var currentTime = new Date().getTime();
						var conversionsObj = provesrc.storage.get(provesrc.constants.storageConversionAnalyticsKey, true);
						for (var k in conversionsObj) {
							if (conversionsObj[k] && conversionsObj[k].t > currentTime && conversionsObj[k].r) {
								returnObj[k] = true;
							}
						}
						provesrc.debug("Conversion events to report:", returnObj);
						return returnObj;
					},
					markReported: function (events) {
						var conversionsObj = provesrc.storage.get(provesrc.constants.storageConversionAnalyticsKey, true);
						for (var k in events) {
							if (events[k]) {
								conversionsObj[k].r = false;
							}
						}
						provesrc.debug("Marked conversion events as reported");
						provesrc.storage.set(provesrc.constants.storageConversionAnalyticsKey, conversionsObj, true);
					}
				},
				reportEvents: function () {
					var analyticsEvents = provesrc.storage.searchKey(provesrc.constants.storageAnalyticsKey);
					if (analyticsEvents && analyticsEvents.length > 0) {
						for (var idx = 0; idx < analyticsEvents.length; idx++) {
							var event = analyticsEvents[idx];
							if (event.val == "true") {
								var eventParams = event.key.split(".");
								var nId = eventParams[0];
								var eventName = eventParams[1];
								provesrc.ANALYTICS.track(eventName, nId);
							}
						}
					} else {
						provesrc.debug("[Analytics] No events to report");
					}
				},
				track: function (event, notificationId, notification) {
					provesrc.ANALYTICS.conversionEvents.report(event);
					provesrc.GOALS.trackGoalEvent(event, notificationId);

					var reqEvent = {id: notificationId, event: event};
					if (isUniqueEvent(event, notificationId)) {
						provesrc.debug("[Analytics] Event:", event, "for notification", notificationId, "is unique");
						//Forward events to enabled trackers
						provesrc.ANALYTICS.externalTrackers.forwardEvent(event, notification || _psThisNotification);
						beforeSendEvent(reqEvent);
					}

					//----Private Helpers

					function isEngagedVisitor() {
						var thisEvent = provesrc.storage.get(provesrc.constants.storageEngagedVisitorKey);
						var currentTime = new Date().getTime();
						if (thisEvent && (thisEvent > currentTime)) {
							return true;
						}
						return false;
					}

					function getEventKey(nId, event) {
						return provesrc.constants.storageAnalyticsKey + "." + nId + "." + event;
					}

					function beforeSendEvent(reqEvent) {
						var eventKey = getEventKey(reqEvent.id, reqEvent.event);
						var thisEvent = provesrc.storage.get(eventKey);
						reqEvent = provesrc.utils.setSegmentUserId(reqEvent);
						if (thisEvent == "true") {
							provesrc.storage.set(eventKey, generateTimestamp());
							if (reqEvent.event == provesrc.constants.analyticsEvents.view && !isEngagedVisitor()) {
								reqEvent.visitor = true;
								provesrc.storage.set(provesrc.constants.storageEngagedVisitorKey, generateTimestamp(true));
								provesrc.debug("[Analytics] √√ Marked visitor as engaged");
							}
							sendEvent(reqEvent);
						}
					}

					function sendEvent(reqEvent) {
						provesrc.API.req(provesrc.API.endpoints.analytics, 'POST', reqEvent, function (err, res, configuration) {
							provesrc.debug("[Analytics] Reported analytics event: --", reqEvent.event, "-- for notification", reqEvent.id);
						});
					}


					function isUniqueEvent(event, notificationId) {
						var eventKey = getEventKey(notificationId, event);
						var thisEvent = provesrc.storage.get(eventKey);
						var currentTime = new Date().getTime();

						if (thisEvent) {
							if (thisEvent == 'true') {
								return true;
							} else if (thisEvent > currentTime) {
								return false;  //Event is NOT unique for this notification
							} else {
								//Event is unique for this notification - update timestamp
								provesrc.storage.set(eventKey, true);
							}
						} else {
							//Event is unique for this notification - update timestamp
							provesrc.storage.set(eventKey, true);
						}
						return true;
					}

					function generateTimestamp(engagedTime) {
						var uniqueEvent = new Date();
						if (engagedTime) {
							uniqueEvent.setDate(uniqueEvent.getDate() + provesrc.constants.engagedUniqueUserTime);
						} else {
							uniqueEvent.setDate(uniqueEvent.getDate() + provesrc.constants.analyticsUniqueEventTime);
						}
						return uniqueEvent.getTime();
					}
				}

			},
			//-------Core Functions
			init: function (options) {
				if (provesrc.storage.getCookie("ps_debug") === 'true') {
					provesrc.settings.debugMode = true;
					provesrc.DEBUG_MODE.start();
				}
				if (!provesrc.VISIBILITY.isOptedOut()) {
					provesrc.settings.apiKey = options.apiKey;
					provesrc.settings.snippetVersion = options.v || "0.0.3";
					provesrc.debug("Loaded with options:", options);
					provesrc.debug("Settings", this.settings);
					provesrc.URL.setupListeners();
					setTimeout(provesrc.URL.setupFormsSubmissionListener, 8000);
					provesrc.debug("Current URL", provesrc.URL.getCurrentURL());
					provesrc.launch();
				}
			},
			launch: function () {
				setUUID();
				provesrc.session.storage.init();
				provesrc.storage.clearOldStorage();
				provesrc.utils.initSegmentIO();
				provesrc.storage.initNewVisitor();
				fetchConfig();
				provesrc.debug("[IE Check] isIE?", provesrc.utils.isIE());

				function fetchConfig() {

					provesrc.API.req(provesrc.API.endpoints.getConfig + provesrc.utils.toBase64(provesrc.URL.getCurrentURL()), 'GET', {}, function (err, res, configuration) {

						provesrc.debug("Got Config", configuration);
						provesrc.settings = merge(provesrc.settings, configuration);
						provesrc.log("Version", provesrc.settings.sdkVersion);
						provesrc.log("Want to boost your sales and conversions like this website does? Visit us at https://provesrc.com");

						provesrc.HTML.injectStyles();
						provesrc.HTML.injectCustomCSS(configuration.customCSS);
						provesrc.HTML.injectCustomCode(configuration.customCode);

						if (provesrc.settings.hasOwnProperty("subscriptionActive") && !provesrc.settings.subscriptionActive) {
							provesrc.log("Your account is inactive - contact <NAME_EMAIL>");
						}

						setTimeout(function () {
							if (provesrc.settings.customFormTracking) {
								var customFormTracking = provesrc.settings.customFormTracking;
								if (customFormTracking.forms && customFormTracking.forms.length > 0) {
									for (var x = 0; x < customFormTracking.forms.length; x++) {
										provesrc.formTypes.others.forms.unshift(customFormTracking.forms[x]);
									}
								}
								if (customFormTracking.submit && customFormTracking.submit.length > 0) {
									for (var y = 0; y < customFormTracking.submit.length; y++) {
										provesrc.formTypes.others.submit.unshift(customFormTracking.submit[y]);
									}
								}
								setInterval(function () {
									if (customFormTracking.firstName && customFormTracking.firstName.length > 0) {
										for (var i = 0; i < customFormTracking.firstName.length; i++) {
											var fields = document.querySelectorAll(customFormTracking.firstName[i]);
											fields.forEach(function (field) {
												if (field && !field.getAttribute(provesrc.constants.psElements.firstName)) {
													field.setAttribute(provesrc.constants.psElements.firstName, provesrc.utils.UUID());
												}
											});
										}
									}
								}, 1000);
							}
						}, 2000)


						if (provesrc.settings.dashboardDebugMode) {
							provesrc.DEBUG_MODE.start();
						}

						provesrc.debug("Loop notifications:", provesrc.settings.loop || false);

						if (provesrc.utils.hasCustomBranding()) {
							provesrc.debug("This account has custom branding");
						} else {
							provesrc.debug("This account doesn't have custom branding / white label");
							provesrc.utils.brandingEnforcement();
						}

						provesrc.GOALS.reportGoals();

						_psCheckDocumentReadyState = window.setInterval(function () {
							provesrc.debug("Waiting for document to load, timer ID:", _psCheckDocumentReadyState);
							if ((document.readyState == "complete" || document.readyState == "interactive") && _psCheckDocumentReadyState != null) {
								provesrc.debug("Page loaded - launching...");
								window.clearInterval(_psCheckDocumentReadyState);
								_psCheckDocumentReadyState = null;
								provesrc.DOM.observe(document.body);
								provesrc.REMARKETING.init(provesrc.settings.remarketing);
								//Not showing notifications if limit reached
								if (provesrc.settings.remarketing && provesrc.settings.remarketing.planLimit) {
									provesrc.debug("Not showing notifications - plan limit reached");
								} else {
									provesrc.API.reporters.ping.initPing();
									provesrc.fetch();
									autoTrackPageForms();
									runDisplayQueue();
									sendConversions();
									provesrc.ANALYTICS.reportEvents();
								}
							}
						}, 100);
					});
				}

				function setUUID() {
					var clientUUID = provesrc.storage.get("xuuid");
					if (!clientUUID) {
						var generatedUUID = provesrc.utils.UUID();
						provesrc.storage.set("xuuid", generatedUUID);
						provesrc.debug("[setUUID]: Registered new client UUID:", generatedUUID)
					} else {
						provesrc.debug("[setUUID]: Client UUID:", clientUUID)
					}
				}

				function autoTrackPageForms() {
					provesrc.debug("Setup form auto-track listeners on this page");
					provesrc.URL.setupFormsSubmissionListener();
				}

				function runDisplayQueue() {
					if (_psDisplayQueue.length > 0) {
						provesrc.debug("Init display queue...");
						for (var i = 0; i < _psDisplayQueue.length; i++) {
							var args = _psDisplayQueue[i];
							if (args.n) {
								args = [args.n, args.g];
							}

							var name = args[0] || null;
							var guid = args[1] || null;
							var params = args[2] || null;
							var callback = args[3] || null;
							provesrc.display(name, guid, params, callback);
						}
					}
				}
			},
			show: function (notifications, resume) {

				if (resume) {
					showNotification(resume);
				} else {
					if (_psIsRunning) {
						provesrc.Q.add(notifications);
						provesrc.debug("Adding", notifications.length, "notifications to queue");
						provesrc.debug("Queue Status:", provesrc.Q.status(), "notifications in queue");
					}

					if (_psIsRunning == false) {
						_psIsRunning = true;
						provesrc.Q.add(notifications);
						provesrc.debug("Initial delay:", provesrc.settings.firstShowDelay, "seconds");
						provesrc.debug("Show for", provesrc.settings.displayHold, "seconds");
						provesrc.debug("Show new every:", provesrc.settings.delayBetweenNotifications, "seconds");
						setTimeout(function () {
							provesrc.debug("Starting...");
							showNotification();
						}, transformSeconds(provesrc.settings.firstShowDelay));
					}
				}

				function showNotification(resume) {
					if (document.readyState != 'complete' && document.readyState != "interactive") return setTimeout(showNotification, 300);

					if (resume && _psThisNotification && _psIsShowingNotification) {
						_psCloseTimeoutTimer = setTimeout(closeNotification, transformSeconds((_psThisNotification.settings.displayHold || provesrc.settings.displayHold), true));
						_psShowTimeoutTimer = setTimeout(showNotification, transformSeconds((_psThisNotification.settings.displayHold || provesrc.settings.displayHold), true)
							+ transformSeconds(provesrc.settings.delayBetweenNotifications, true))
					} else if (!_psIsShowingNotification && document.visibilityState == "visible") {
						if (provesrc.Q.status() > 0) {
							var shouldLoop = false;
							if (provesrc.Q.status() > 1) {
								shouldLoop = true;
							}
							_psThisNotification = provesrc.Q.pull();
							if (provesrc.checkNotificationSettings(_psThisNotification)) { // Check if should display according to settings
								if (provesrc.settings.loop && shouldLoop) {
									provesrc.Q.push(_psThisNotification);
								}
								var notificationHTML = provesrc.HTML.generateNotificationHTML(_psThisNotification);
								provesrc.HTML.injectContainer(notificationHTML, function () {
									provesrc.HTML.setNotificationSettingsAfterLoad(_psThisNotification);
									provesrc.HTML.setNotificationVisibility(true);
									provesrc.ANALYTICS.track(provesrc.constants.analyticsEvents.view, _psThisNotification._id, _psThisNotification);
									provesrc.debug("Left to show:", provesrc.Q.status());
									_psCloseTimeoutTimer = setTimeout(closeNotification, transformSeconds((_psThisNotification.settings.displayHold || provesrc.settings.displayHold), true));
									_psShowTimeoutTimer = setTimeout(showNotification, transformSeconds((_psThisNotification.settings.displayHold || provesrc.settings.displayHold), true)
										+ transformSeconds(provesrc.settings.delayBetweenNotifications, true))
								});
							} else {
								provesrc.debug("Not showing notification [settings]");
								showNotification();
							}
						} else {
							provesrc.debug("Done showing notifications");
							_psIsShowingNotification = false;
							clearTimeout(_psShowTimeoutTimer);
							clearTimeout(_psCloseTimeoutTimer);
							_psIsRunning = false;
						}

					}
				}

				function closeNotification() {
					provesrc.HTML.setNotificationVisibility(false);
					setTimeout(function () {
						provesrc.HTML.removeContainer();
					}, transformSeconds(1));
				}

			},
			stop: function (days) {
				if (days) {
					provesrc.VISIBILITY._psOptOut(days);
					provesrc.debug("[STOP]: Stopping notifications for", days, "days");
				} else {
					provesrc.debug("[STOP]: Stopping notifications for this current session only");
				}
				sessionStorageSetItem(provesrc.constants.storageHideSessionNotifications, true);
			},
			checkNotificationSettings: function (n) {
				provesrc.debug("[Visitor Type] This notification will be shown to", n.settings.visitor.type, "visitors");

				if (provesrc.session.storage.notificationsDisabledInSession()) {
					return false;
				}
				//Hide notification on mobile screens
				if (provesrc.utils.isMobile() && n.settings.mobileDesign && n.settings.mobileDesign === 'hide') {
					provesrc.debug("Mobile view not enabled for this notification");
					return false;
				}
				if (n.settings.sessionShowOnce) {
					provesrc.debug("Settings: Should show notification once in a session");
					if (provesrc.session.storage.didViewNotification(n)) {
						return false;
					}
				}
				return true;
			},
			fetch: function () {
				if (provesrc.settings.stop) {
					return;
				}
				provesrc.API.req(provesrc.API.endpoints.getNotifications, 'POST', {
					url: provesrc.URL.getCurrentURL(),
					unique: provesrc.URL.isURLUniqueVisitor(),
					first: provesrc.storage.isNewVisitor()
				}, function (err, res, notifications) {
					provesrc.debug("[fetchNotifications] Got", notifications.length, "notifications");
					notifications = filterNotifications(notifications);
					provesrc.debug("[fetchNotifications] Got", notifications.length, "notifications after filtering");

					if (provesrc.settings.randomize) {
						provesrc.debug("Shuffeling notifications...");
						notifications = shuffleNotifications(notifications);
					}
					provesrc.show(notifications);
				});
			},
			resume: function () {
				sessionStorageRemoveItem(provesrc.constants.storageHideSessionNotifications);
				provesrc.fetch();
			},
			display: function (name, guid, dynamicParams, callback) {
				if (name) {
					this.debug("Manually show notification:", arguments);

					var params = {
						name: name,
						first: provesrc.storage.isNewVisitor(),
						dynamicParams: dynamicParams
					};
					if (guid) params.guid = guid;

					provesrc.API.req(provesrc.API.endpoints.getNotifications, 'POST', params, function (err, res, notifications) {
						provesrc.debug("Got", notifications.length, "notifications to show manually by code.");
						if (dynamicParams) {
							for (var x = 0; x < notifications.length; x++) {
								notifications[x].message = provesrc.utils.compileTemplate(notifications[x].message, dynamicParams);
								if (dynamicParams.image) {
									notifications[x].image = dynamicParams.image;
								}
							}
						}
						if (provesrc.settings.randomize) {
							provesrc.debug("Shuffeling notifications...");
							notifications = shuffleNotifications(notifications);
						}
						provesrc.show(notifications);
						if (callback) {
							return callback(err, !!(notifications && notifications.length));
						}
					});

				} else {
					provesrc.log("You must pass notification name in order to display a notification.");
				}
			},
			trackGoal: function (goalId) {
				if (!goalId || goalId.length === 0 || goalId == "")
					return console.error("[provesrc.trackGoal]: ProveSource: Goal ID is empty.") && provesrc.debug("[provesrc.trackGoal]: Goal ID is empty.");

				provesrc.GOALS.reportGoals(goalId);
			}
		};

//Initiate
		if (window._provesrcAsyncInit) {
			window._provesrcAsyncInit();
		} else if (window._proofsrcAsyncInit) {
			window._proofsrcAsyncInit();
		} else {
			provesrc.debug("Checking apiKey in script src url...");
			var pageScripts = document.getElementsByTagName("script");
			var psScript = null;
			for (var i = 0; i < pageScripts.length; i++) {
				var script = pageScripts[i];
				if (script.src.indexOf('cdn.provesrc.com') > -1 || script.src.indexOf('provesrc.js?apiKey=') > -1) {
					psScript = script;
					break;
				}
			}
			var extractedApiKey = getParamValuesByName(psScript.src, "apiKey");
			provesrc.debug("Got apiKey from script src url:", extractedApiKey);
			provesrc.init({apiKey: extractedApiKey})
		}

//---//

		function getLocalStorage() {
			try {
				return localStorageRef;
			} catch (e) {
				provesrc.debug("localStorage is not accessible");
				return null;
			}
		}

		function localStorageGetItem(key) {
			try {
				return localStorageRef.getItem(key);
			} catch (error) {
				provesrc.debug("localStorage is not accessible");
				return null;
			}
		}

		function localStorageRemoveItem(key) {
			try {
				localStorageRef.removeItem(key);
			} catch (error) {
				provesrc.debug("localStorage is not accessible");
			}
		}

		function localStorageSetItem(key, value) {
			try {
				return localStorageRef.setItem(key, value);
			} catch (error) {
				provesrc.debug("localStorage is not accessible");
			}
		}

		function sessionStorageGetItem(key) {
			try {
				return sessionStorage.getItem(key);
			} catch (error) {
				provesrc.debug("sessionStorage is not accessible");
				return null;
			}
		}

		function sessionStorageRemoveItem(key) {
			try {
				sessionStorage.removeItem(key);
			} catch (error) {
				provesrc.debug("sessionStorage is not accessible");
			}
		}

		function sessionStorageSetItem(key, value) {
			try {
				return sessionStorage.setItem(key, value);
			} catch (error) {
				provesrc.debug("sessionStorage is not accessible");
			}
		}


		function getParamValuesByName(href, querystring) {
			var qstring = href.slice(href.indexOf('?') + 1).split('&');
			for (var i = 0; i < qstring.length; i++) {
				var urlparam = qstring[i].split('=');
				if (urlparam[0] == querystring) {
					return urlparam[1];
				}
			}
		}

		function numberFormatter(n, number) {
			if (number) {
				return number.toLocaleString();
			}
			if (n.settings.hideExactNumber && n.settings.hideExactNumber.active && (n.count > n.settings.hideExactNumber.max)) {
				return n.settings.hideExactNumber.max.toLocaleString() + "+";
			} else {
				return n.count.toLocaleString();
			}
		}

		function transformSeconds(s, c) {
			if (c && s <= 1) s = 2;
			return s * 1000
		}

		function merge() {
			var obj = {},
				i = 0,
				il = arguments.length,
				key;
			for (; i < il; i++) {
				for (key in arguments[i]) {
					if (arguments[i].hasOwnProperty(key)) {
						obj[key] = arguments[i][key];
					}
				}
			}
			return obj;
		};

		function _psGetWidgetStyle() {
			var _psWidgetStyles = {
				defaultStyle: "#provesrc-debug-mode *,#provesrc-notification-container *{box-sizing:border-box;line-height:17px;margin:0;padding:0;border:0;font-size:100%!important;vertical-align:baseline;letter-spacing:0!important}#provesrc-debug-mode .provesrc-logo>img{margin-left:auto;margin-right:auto}#provesrc-notification-container .pfs-link svg{display:inline-block!important;width:13px!important;height:13px!important;margin-bottom:-2px}#provesrc-debug-mode .debug-mode-container span.green-dot{display:inline-block;width:9px;margin-right:5px;height:9px;background:#42ac05;border-radius:50%}#provesrc-debug-mode .debug-mode-container img{width:35px;text-align:center}#provesrc-debug-mode .debug-mode-container{text-align:center}#provesrc-debug-mode .installed-note{margin-bottom:2px;font-size:.9rem;color:#7205f7!important}#provesrc-debug-mode span.admin-note{color:#838383;font-weight:400}#provesrc-debug-mode{background-color:#fff;bottom:46%;color:#7205f7!important;left:-4px;font-size:.8rem;font-weight:700;padding:8px;border:3px solid #7225f3;border-top-right-radius:10px;border-bottom-right-radius:10px}#provesrc-debug-mode,#provesrc-widget-area{z-index:2147483647}#provesrc-debug-mode,#provesrc-notification-container{position:fixed!important;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .bubble-body.rtl-bubble.combo-type .bubble-icon{padding-right:0!important;padding-left:5px}#provesrc-notification-container .bubble-body.rtl-bubble.combo-type .bubble-content{margin-right:auto;margin-left:0;padding-left:21px;padding-right:0;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .bubble-body.rtl-bubble{direction:rtl!important;text-align:right!important;font-family:Arial,sans-serif}#provesrc-notification-container .bubble-body.rtl-bubble .bubble-content .bubble-cta .cta-arrow::after{border-right:0;border-top:0;border-left:.1em solid;border-bottom:.1em solid;margin-left:0;margin-right:.3em}#provesrc-notification-container .bubble-body.rtl-bubble .bubble-icon{border-right:0}#provesrc-notification-container .bubble-body.rtl-bubble #ps-bubble-close{left:3px!important;right:inherit!important}#provesrc-notification-container .bubble-body.rtl-bubble .bubble-content{display:flex;min-width:0;margin-right:0;padding:7px;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container.bottom-center .bubble-body,#provesrc-notification-container.bottom-left .bubble-body,#provesrc-notification-container.top-left .bubble-body{float:left}#provesrc-notification-container.bottom-right .bubble-body,#provesrc-notification-container.top-right .bubble-body{float:right}#provesrc-notification-container.bottom-center{left:50%;bottom:10px;margin-left:-165px}#provesrc-notification-container.bottom-left{left:10px;bottom:10px}#provesrc-notification-container.bottom-right{right:10px;bottom:10px}#provesrc-notification-container.top-right{right:10px;top:10px}#provesrc-notification-container.top-left{left:10px;top:10px}#provesrc-notification-container .bubble-body:hover{box-shadow:0 6px 20px rgba(0,0,0,.2);-webkit-box-shadow:0 6px 20px rgba(0,0,0,.2);-moz-box-shadow:0 6px 20px rgba(0,0,0,.2);-webkit-transition:.3s;-moz-transition:.3s;-ms-transition:.3s;-o-transition:.3s;transition:.3s;top:-10px}#provesrc-notification-container .bubble-body{display:flex;align-items:center;position:relative;cursor:pointer!important;direction:ltr!important;text-align:left!important;width:330px;overflow:hidden;border-radius:6px;background-color:#fff;box-shadow:0 5px 20px rgba(0,0,0,.15)!important;-webkit-box-shadow:0 5px 20px rgba(0,0,0,.15)!important;-moz-box-shadow:0 5px 20px rgba(0,0,0,.15)!important;border:1px solid #ececec;font-family:Lato,arial,sans-serif!important;transition:.3s;top:0}#provesrc-notification-container .bubble-content .bubble-cta .cta-arrow::after{position:relative;content:'';display:inline-block;width:.4em;height:.4em;border-right:.1em solid;border-top:.1em solid;transform:rotate(45deg)!important;margin-left:.3em;font-size:17px;-webkit-animation:2s infinite provesrc-arrow-bouncer;animation:2s infinite provesrc-arrow-bouncer}#provesrc-notification-container .bubble-content .bubble-cta{line-height:17px;font-size:12px!important;color:#a0a0a0;padding:3px 0;border-radius:5px;margin-bottom:5px;margin-top:6px;display:inline-block}#provesrc-notification-container .bubble-body .bubble-content{min-width:0;margin-left:0;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .bubble-body.stream-type .bubble-content{display:flex;padding:7px;align-items:center}#provesrc-notification-container .bubble-body.review-type .bubble-icon img{max-width:55px;max-height:60px}#provesrc-notification-container .bubble-body .bubble-content-inner{flex:1 1 0%;min-width:0;margin:auto}#provesrc-notification-container .bubble-body .bubble-content .bubble-time a,#provesrc-notification-container .bubble-body .bubble-content .bubble-time span{display:inline!important;font-size:11px!important;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .bubble-body .bubble-content .bubble-time{display:flex;gap:5px;font-size:11px!important;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .bubble-body.review-type .bubble-content .bubble-time{display:inline!important;gap:5px;font-size:11px!important;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .bubble-body .bubble-content .bubble-description>span{font-size:12px!important}#provesrc-notification-container .bubble-body .bubble-content .bubble-description{line-height:17px;font-size:12px!important;margin-bottom:2px;margin-top:2px;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .bubble-body .bubble-content .ps-bubble-title span{font-weight:700;display:inline-block;padding:3px 5px;border-radius:3px;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .bubble-body .bubble-content .ps-bubble-title{line-height:17px;font-size:15px!important;font-weight:700;font-family:Lato,arial,sans-serif!important;margin-bottom:2px}#provesrc-notification-container .bubble-body .bubble-icon{min-width:50px;min-height:55px;display:flex;position:relative}#provesrc-notification-container .bubble-body .bubble-icon svg{flex:0 0 auto}#provesrc-notification-container .bubble-body .bubble-time .pfs-link a{text-decoration:none;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .bubble-body .bubble-time .pfs-link span.bubble-bolt>i{display:inline-block!important}#provesrc-notification-container .bubble-body .bubble-time .pfs-link{display:inline-block;font-size:11px!important;font-family:Lato,arial,sans-serif!important}#provesrc-notification-container .text-capitalize{text-transform:capitalize}#provesrc-notification-container .text-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}#provesrc-notification-container .no-text-select{-webkit-touch-callout:none;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}#provesrc-notification-container .mx-auto{margin-right:auto!important;margin-left:auto!important}#provesrc-notification-container.ps-show{display:block}#provesrc-notification-container.ps-hide{display:none}#provesrc-notification-container #ps-bubble-close:hover{opacity:.8}#provesrc-notification-container #ps-bubble-close{position:absolute;top:3px;right:3px;height:13px;width:13px;transform:rotate(45deg);cursor:pointer;opacity:.5;zoom:1.3}#provesrc-widget-area .ps-animated{-webkit-animation-duration:.5s;animation-duration:.5s;-webkit-animation-fill-mode:both;animation-fill-mode:both}#provesrc-notification-container .no-visible{visibility:hidden}#provesrc-notification-container .is-visible{visibility:visible}#provesrc-notification-container .bubble-body.stream-type a#ps-stream-product-link:hover{-webkit-text-decoration:underline!important;-moz-text-decoration:underline!important;-ms-text-decoration:underline!important;-o-text-decoration:underline!important;text-decoration:underline!important;cursor:pointer!important}#provesrc-notification-container .bubble-body.stream-type a#ps-stream-no-product-link,#provesrc-notification-container .bubble-body.stream-type a#ps-stream-product-link{font-weight:700!important;color:#454545;-webkit-text-decoration:none;-moz-text-decoration:none;-ms-text-decoration:none;-o-text-decoration:none;text-decoration:none}#provesrc-notification-container .bubble-body.stream-type .bubble-icon{border-right:none}#provesrc-notification-container .bubble-body.stream-type span.stream-location>img.location-flag{max-width:17px!important;width:17px!important;border-radius:2px!important;vertical-align:text-top!important;border:1px solid #d2d2d2!important;margin-left:4px!important}#provesrc-notification-container .bubble-body.stream-type .stream-location{font-size:12px!important;opacity:.6!important;font-weight:400!important;color:inherit!important}#provesrc-notification-container .bubble-body.review-type .bubble-icon span.bubble-initials,#provesrc-notification-container .bubble-body.stream-type .bubble-icon span.bubble-initials{font-weight:bolder;font-size:27px!important;align-content:center;height:55px}#provesrc-notification-container .bubble-body.stream-type .bubble-icon img{max-width:65px;max-height:55px}#provesrc-notification-container .bubble-body.stream-type .bubble-content .ps-bubble-title span{background-color:transparent;padding:0!important;max-width:160px;vertical-align:middle}#provesrc-notification-container .bubble-body.stream-type .bubble-content .bubble-description{margin-top:0}#provesrc-notification-container .bubble-body.stream-type.rtl-bubble{border-radius:5px}#provesrc-notification-container .bubble-body.stream-type.rtl-bubble .bubble-icon img{right:2px}#provesrc-notification-container .bubble-body.stream-type.rtl-bubble .bubble-icon{border-right:0;border-left:none}#provesrc-notification-container .bubble-body.review-type.rtl-bubble .bubble-icon span.bubble-initials,#provesrc-notification-container .bubble-body.stream-type.rtl-bubble .bubble-icon span.bubble-initials{margin-right:1px}#provesrc-notification-container .bubble-body.combo-type{background-color:#fff;width:350px}#provesrc-notification-container .bubble-body.rtl-bubble.combo-type .bubble-icon{border-left:0}#provesrc-notification-container .bubble-body.combo-type .bubble-icon{border-right:0;width:35%;padding-right:5px}#provesrc-notification-container .bubble-body.combo-type .bubble-time{color:#7627f3}#provesrc-notification-container .bubble-body.combo-type .bubble-time .pfs-link a{color:#7627f3;text-decoration:none;font-weight:700}#provesrc-notification-container .bubble-body.combo-type .bubble-time .pfs-link{padding:1px 5px;background-color:#fff;border-radius:2px;margin-top:5px;border:1px solid #7627f3;color:#7627f3}#provesrc-notification-container .bubble-body.combo-type .bubble-content.no-branding{padding-top:20px;padding-bottom:20px}#provesrc-notification-container .bubble-body.combo-type .bubble-content .bubble-description{color:#7627f3;font-size:12px}#provesrc-notification-container #ps-bubble-close>span.close-before{content:'';display:block;width:100%;height:3px;background-color:#c4c4c4;position:absolute;left:0;top:5px}#provesrc-notification-container #ps-bubble-close>span.close-after{content:'';display:block;height:100%;width:3px;background-color:#c4c4c4;position:absolute;left:5px;top:0}#provesrc-notification-container .bubble-body.combo-type #ps-bubble-close>span.close-after,#provesrc-notification-container .bubble-body.combo-type #ps-bubble-close>span.close-before{background-color:#7627f3}#provesrc-notification-container .bubble-body.combo-type .bubble-content{margin-left:auto;padding-top:10px;padding-right:21px;padding-bottom:10px;width:65%;margin-top:5px;margin-bottom:5px}#provesrc-notification-container .bubble-body.combo-type .bubble-icon .combo-number>.refer{font-weight:400!important;font-size:12px!important}#provesrc-notification-container .bubble-body.combo-type .bubble-icon .combo-number>.num-value{font-size:28px!important;font-weight:900!important;line-height:30px!important}#provesrc-notification-container .scale{animation:1s 1s 3 pound}@keyframes pound{from,to{transform:none}50%{transform:scale(1.2)}}.provesrc-arrow-bouncer{-webkit-animation:2s infinite provesrc-arrow-bouncer;animation:2s infinite provesrc-arrow-bouncer}@-webkit-keyframes provesrc-arrow-bouncer{0%,100%,20%,50%,80%{-webkit-transform:translateX(0) rotate(45deg);transform:translateX(0) rotate(45deg)}40%{-webkit-transform:translateX(-5px) rotate(45deg);transform:translateX(-5px) rotate(45deg)}60%{-webkit-transform:translateX(-3px) rotate(45deg);transform:translateX(-3px) rotate(45deg)}}@keyframes provesrc-arrow-bouncer{0%,100%,20%,50%,80%{-webkit-transform:translateX(0) rotate(45deg);transform:translateX(0) rotate(45deg)}40%{-webkit-transform:translateX(-5px) rotate(45deg);transform:translateX(-5px) rotate(45deg)}60%{-webkit-transform:translateX(-3px) rotate(45deg);transform:translateX(-3px) rotate(45deg)}}#provesrc-notification-container .bubble-body.combo-type .bubble-icon .combo-number{position:relative;float:left;top:50%;left:50%;transform:translate(-50%,-50%);color:#7627f3;width:100%;text-align:center}@-webkit-keyframes psFadeInUp{from{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}@keyframes psFadeInUp{from{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}.psFadeInUp{-webkit-animation-name:psFadeInUp;animation-name:psFadeInUp}@-webkit-keyframes fadeInUp{from{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}@keyframes psBounceInUp{from{opacity:.5;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}.psBounceInUp{-webkit-animation-name:psBounceInUp;animation-name:psBounceInUp}@-webkit-keyframes psBounceInDown{from{opacity:.5;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}@keyframes psBounceInDown{from{opacity:.5;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}.psBounceInDown{-webkit-animation-name:psBounceInDown;animation-name:psBounceInDown}@-webkit-keyframes psFadeOutDown{from{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}@keyframes psFadeOutDown{from{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}.psFadeOutDown{-webkit-animation-name:psFadeOutDown;animation-name:psFadeOutDown}@-webkit-keyframes psFadeOutUp{from{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}}@keyframes psFadeOutUp{from{opacity:1}to{opacity:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}}.psFadeOutUp{-webkit-animation-name:psFadeOutUp;animation-name:psFadeOutUp}@media only screen and (max-width:815px){#provesrc-notification-container{bottom:0!important;left:0!important;width:100%;max-width:100%}#provesrc-notification-container._ps-mobile-small>.bubble-body{width:100%;max-width:75%!important;zoom:0.8;border-radius:0}#provesrc-notification-container._ps-mobile-small>.review-type ._ps-review-rating>.review-source-container{margin-right:2px!important}#provesrc-notification-container._ps-mobile-small ._ps-review-rating>.review-source-container>span.review-source-name{display:none!important}#provesrc-notification-container .bubble-body:hover{box-shadow:none;-webkit-box-shadow:none;-moz-box-shadow:none;-webkit-transition:none;-moz-transition:none;-ms-transition:none;-o-transition:none;transition:none;top:0}#provesrc-notification-container .bubble-body.combo-type,#provesrc-notification-container .bubble-body.live-type{width:100%!important}#provesrc-notification-container>.bubble-body{width:100%;max-width:100%;border-radius:0}#provesrc-notification-container .bubble-content .bubble-cta{margin-bottom:0;margin-top:0}#provesrc-notification-container.top-left,#provesrc-notification-container.top-right{right:0!important;top:0!important;height:50px}#provesrc-notification-container.bottom-center{margin-left:0}#provesrc-notification-container.bottom-center,#provesrc-notification-container.bottom-left,#provesrc-notification-container.bottom-right{right:0!important;bottom:0!important}#provesrc-notification-container #ps-bubble-close span.close-after{height:13px!important}#provesrc-notification-container #ps-bubble-close span.close-before{width:13px!important}#provesrc-notification-container #ps-bubble-close{padding:15px!important;top:11px!important;right:-1px!important}#provesrc-notification-container>.bubble-body.live-type>.bubble-live-pulse{float:left;width:67px;display:block;position:absolute;border-right:none;height:100%;margin-top:0!important}#provesrc-notification-container>.bubble-body.live-type>.bubble-live-pulse>svg{height:50px!important;padding:5px;margin-top:auto;margin-bottom:auto;position:absolute;top:0;bottom:0;left:0;right:0}#provesrc-notification-container>.bubble-body.live-type>.bubble-content>.bubble-time{flex:0 1 100%;margin-top:4px}#provesrc-notification-container>.bubble-body.live-type>.bubble-content>.bubble-description>span{vertical-align:middle}#provesrc-notification-container>.bubble-body.live-type>.bubble-content>.bubble-description{display:inline-block;font-size:14px!important;margin-bottom:0;line-height:inherit!important;margin-top:0!important}#provesrc-notification-container>.bubble-body.live-type>.bubble-content>.ps-bubble-title>span{vertical-align:sub;font-weight:bolder!important;display:inline-block!important;line-height:inherit!important;padding:2px 8px!important;border-radius:4px!important;min-width:0!important}#provesrc-notification-container>.bubble-body.live-type>.bubble-content>.ps-bubble-title{margin-right:6px;line-height:inherit!important;font-size:15px!important;margin-top:0!important;display:inline-block!important}#provesrc-notification-container>.bubble-body.live-type>.bubble-content{margin-left:70px!important;min-height:60px;display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;text-align:unset!important}#provesrc-notification-container .bubble-body.live-type.rtl-bubble>.bubble-content>.ps-bubble-title{margin-left:0!important;margin-right:5px!important}#provesrc-notification-container .bubble-body.live-type.rtl-bubble>.bubble-content{margin-left:0!important;margin-right:70px!important}}#provesrc-bubble-body-container.bubble-body.plan-limit-reached>.limit-content>strong{color:#f03e45;padding:2px 5px;background:#ffefef;font-size:1rem;font-weight:700;border-radius:6px}#provesrc-bubble-body-container.bubble-body.plan-limit-reached>.box-title{padding:6px 7px 6px 6px;background:#fff;color:#6e6e6e;border-radius:3px;font-size:14px;margin-left:auto;margin-right:auto;margin-bottom:0;display:inline-block;text-align:left;width:auto;border:1px solid #d7d7d7}#provesrc-bubble-body-container.bubble-body.plan-limit-reached>.limit-content{font-size:.9rem;color:#767676;margin-top:20px;text-align:left;line-height:1.4;font-weight:500}#provesrc-bubble-body-container.bubble-body.plan-limit-reached>.bubble-time{text-align:center;padding:7px 0}#provesrc-bubble-body-container.bubble-body.plan-limit-reached>.limit-title{color:#151515;margin-left:auto;margin-right:auto;text-align:left;font-size:1.3rem;margin-top:15px}#provesrc-bubble-body-container.bubble-body.plan-limit-reached{font-size:.9rem;text-align:center;width:485px;padding:1.9rem;z-index:2147483010!important}#provesrc-remarketing-container{position:absolute;width:300px;border-radius:3px;background:#fff;text-align:center;left:0;bottom:5px;border:3px solid #7625f3;padding:5px;min-height:100px;cursor:pointer}#provesrc-bubble-body-container.plan-limit-reached .lock-icon,#provesrc-debug-mode .lock-icon,#provesrc-remarketing-container .lock-icon{background-image:url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pg0KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE2LjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPg0KPCFET0NUWVBFIHN2ZyBQVUJMSUMgIi0vL1czQy8vRFREIFNWRyAxLjEvL0VOIiAiaHR0cDovL3d3dy53My5vcmcvR3JhcGhpY3MvU1ZHLzEuMS9EVEQvc3ZnMTEuZHRkIj4NCjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCINCgkgd2lkdGg9IjM2MS4xMThweCIgaGVpZ2h0PSIzNjEuMTE4cHgiIHZpZXdCb3g9IjAgMCAzNjEuMTE4IDM2MS4xMTgiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDM2MS4xMTggMzYxLjExODsiDQoJIHhtbDpzcGFjZT0icHJlc2VydmUiPg0KPGc+DQoJPGcgaWQ9Il94MzJfMzcuX0xvY2tlZCI+DQoJCTxnPg0KCQkJPHBhdGggZD0iTTI3NC43NjUsMTQxLjNWOTQuMjA1QzI3NC43NjUsNDIuMTcyLDIzMi41ODMsMCwxODAuNTU5LDBjLTUyLjAzMiwwLTk0LjIwNSw0Mi4xNzItOTQuMjA1LDk0LjIwNVYxNDEuMw0KCQkJCWMtMTcuMzQsMC0zMS40LDE0LjA2LTMxLjQsMzEuNHYxNTcuMDE2YzAsMTcuMzQ0LDE0LjA2LDMxLjQwMiwzMS40LDMxLjQwMmgxODguNDExYzE3LjM0MSwwLDMxLjM5OC0xNC4wNTksMzEuMzk4LTMxLjQwMlYxNzIuNw0KCQkJCUMzMDYuMTY0LDE1NS4zNiwyOTIuMTA2LDE0MS4zLDI3NC43NjUsMTQxLjN6IE0xMTcuNzU2LDk0LjIwNWMwLTM0LjY5LDI4LjEyLTYyLjgwMyw2Mi44MDMtNjIuODAzDQoJCQkJYzM0LjY4NSwwLDYyLjgwNSwyOC4xMTIsNjIuODA1LDYyLjgwM1YxNDEuM0gxMTcuNzU2Vjk0LjIwNXogTTI3NC43NjUsMzI5LjcxNUg4Ni4zNTRWMTcyLjcwOGgxODguNDExVjMyOS43MTV6DQoJCQkJIE0xNjQuODU4LDI2Mi41NTh2MjAuMDU0YzAsOC42NjQsNy4wMzUsMTUuNzAxLDE1LjcwMSwxNS43MDFjOC42NjQsMCwxNS43MDEtNy4wMzcsMTUuNzAxLTE1LjcwMXYtMjAuMDU0DQoJCQkJYzkuMzM3LTUuNDQxLDE1LjcwMS0xNS40NTYsMTUuNzAxLTI3LjA0NmMwLTE3LjM0OC0xNC4wNjItMzEuNDEtMzEuNDAyLTMxLjQxYy0xNy4zNCwwLTMxLjQsMTQuMDYyLTMxLjQsMzEuNDENCgkJCQlDMTQ5LjE1OSwyNDcuMTAyLDE1NS41MTcsMjU3LjExNywxNjQuODU4LDI2Mi41NTh6Ii8+DQoJCTwvZz4NCgk8L2c+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8L3N2Zz4NCg==');background-size:14px;display:inline-block;width:18px;opacity:.3;height:14px;background-repeat:no-repeat;margin-bottom:-1px}#provesrc-bubble-body-container.plan-limit-reached .money-back{color:#03ab33!important;padding-bottom:5px;font-weight:700}#provesrc-bubble-body-container .limit-cta{margin-top:10px}#provesrc-bubble-body-container .limit-cta>a.box-cta{display:block;padding:7px 0;background:#03ab33;color:#fff;border:2px solid #03ab33;border-radius:5px;width:100px;font-size:.9rem;text-align:center;margin-right:auto;font-weight:700}#provesrc-remarketing-container a.box-cta{display:block;padding:6px;background:#7625f3;color:#fff;border:1px solid #6d1fe7;border-radius:3px}#provesrc-remarketing-container .box-content{padding:15px;margin-top:0;font-size:14px;line-height:19px!important}#provesrc-bubble-body-container .box-title,#provesrc-remarketing-container .box-title{padding:5px;background:#f7f7f7;color:#9f9f9f;border-radius:3px;margin-left:auto;margin-right:auto;font-size:14px;text-align:center}#provesrc-bubble-body-container .box-title{width:100%;margin-bottom:8px}#provesrc-remarketing-container .box-title{width:60%}#provesrc-notification-container .bubble-body.live-type.rtl-bubble>.bubble-content>.ps-bubble-title{margin-left:5px;margin-right:0}#provesrc-notification-container .bubble-body.live-type.rtl-bubble>.bubble-content{margin-left:0;margin-right:67px}#provesrc-notification-container .bubble-body.live-type .bubble-content{margin-left:70px}#provesrc-notification-container .bubble-body.live-type{background-color:#fff;width:290px}#provesrc-notification-container .bubble-body.live-type .bubble-content>.ps-bubble-title>span{font-weight:bolder;display:inline-block;border-radius:4px;background-color:#f2e9ff;color:#7825f3}#provesrc-notification-container .bubble-body.live-type .bubble-content .bubble-time{margin-top:5px;margin-bottom:2px}#provesrc-notification-container .bubble-body.live-type .bubble-content>.ps-bubble-title{font-size:14px;display:inline;margin-right:5px}#provesrc-notification-container .bubble-body.live-type svg.ps-pulse-dot>circle{stroke:#24a03c;fill:#24a03c!important;stroke-width:1px;stroke-opacity:1}#provesrc-notification-container .bubble-body.live-type svg.ps-pulse-dot{position:inherit;width:100%;height:100%;display:block}#provesrc-notification-container .bubble-body.live-type svg.ps-pulse-dot ._ps-pulse{fill:white!important;fill-opacity:0;transform-origin:50% 50%;animation-duration:2s;animation-name:_ps-pulse;animation-iteration-count:infinite}#provesrc-notification-container .bubble-body.live-type>.bubble-content>.bubble-description{font-size:14px;display:inline-block}@keyframes _ps-pulse{from{stroke-width:3px;stroke-opacity:1;transform:scale(.3)}to{stroke-width:0;stroke-opacity:0;transform:scale(2)}}#provesrc-notification-container .bubble-body.informational-type .bubble-content .ps-bubble-title span{padding:3px 0;background-color:transparent!important;border-radius:0;color:#2d2d2d}#provesrc-notification-container .bubble-body.informational-type .bubble-content .bubble-description{font-size:12px!important;color:#5e5e5e!important;margin-bottom:4px;margin-top:2px;padding-right:10px}#provesrc-notification-container .bubble-body.informational-type{min-height:60px;display:flex;align-items:center}#provesrc-notification-container .bubble-body.informational-type.rtl-bubble .bubble-content .bubble-description{padding-right:0;padding-left:10px}#provesrc-notification-container .bubble-body.informational-type.rtl-bubble .bubble-time .ps-link{direction:ltr!important}#provesrc-notification-container .bubble-body.informational-type .bubble-time .ps-link{margin-left:-2px}#provesrc-notification-container .bubble-body.rtl-bubble.review-type .review-source-container{margin-right:0}#provesrc-notification-container .bubble-body.rtl-bubble.review-type ._ps-review-rating,#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-description>span{padding-right:0;padding-left:5px}#provesrc-notification-container .bubble-body.rtl-bubble.review-type .review-source-container .review-source-name{display:none;margin-left:5px}#provesrc-notification-container .bubble-body.rtl-bubble.review-type ._ps-review-rating img._ps-review-source{margin-right:0;margin-left:5px}#provesrc-notification-container .bubble-body.rtl-bubble.review-type .bubble-time .pfs-link{float:left;margin-left:20px}#provesrc-notification-container .bubble-body.review-type .bubble-time .pfs-link{float:right;margin-right:20px}#provesrc-notification-container .bubble-body.review-type .bubble-icon{padding-right:0;padding-left:7px}#provesrc-notification-container .bubble-body.review-type .bubble-icon svg{min-width:55px;min-height:55px}#provesrc-notification-container .bubble-body.review-type .bubble-description>span{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;line-height:16px;margin-bottom:8px;padding-right:5px;max-height:50px;-webkit-line-clamp:3;-webkit-box-orient:vertical;-o-text-overflow:ellipsis;-ms-text-overflow:ellipsis;font-style:italic;font-size:12px}#provesrc-notification-container .bubble-body.review-type .ps-bubble-title>span{padding:3px 0}#provesrc-notification-container .bubble-body.review-type ._ps-review-rating img._ps-review-source{width:11px;margin-right:4px;display:inline-block;vertical-align:text-bottom}#provesrc-notification-container .bubble-body.review-type ._ps-review-rating{display:inline-block;padding-right:5px}#provesrc-notification-container .bubble-body.review-type .review-source-container{display:inline-block;margin-right:8px}#provesrc-notification-container svg{position:unset;top:unset;left:unset}.map-container{position:relative;display:inline-block;width:100px;height:100px}.map-container img{width:100%;height:100%;display:block}.map-overlay{position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.4);pointer-events:none;border-radius:8px}#provesrc-notification-container .bubble-body.review-type ._ps-review-rating ._ps-review-rating-star svg{display:inline!important;color:#ffc100!important;fill:rgb(255,193,0)}#provesrc-notification-container .bubble-body.review-type ._ps-review-rating ._ps-review-rating-star{color:#ffc100;font-size:10px!important;vertical-align:middle}#provesrc-notification-container .bubble-body.social-type{display:flex;justify-content:center;flex-direction:column}#provesrc-notification-container .bubble-body.social-type .social-text{font-size:14px;margin:9px 13px 0;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:normal;word-wrap:normal;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;-o-text-overflow:ellipsis;-ms-text-overflow:ellipsis;line-height:17px;max-height:35px}#provesrc-notification-container .bubble-body.social-type .bubble-description .social-item img{width:40px;display:block;margin:0 auto 2px}#provesrc-notification-container .bubble-body.social-type .bubble-description .social-item span{padding-top:1px;display:block;margin:0 auto 5px;text-align:center;font-weight:700;font-size:12px!important}#provesrc-notification-container .bubble-body.social-type .bubble-description{display:flex;justify-content:center;align-items:flex-start;gap:1.5rem;padding-left:1rem;padding-right:1rem;padding-top:1.5rem;width:100%}#provesrc-notification-container .bubble-body.social-type .bubble-description .social-item{display:flex;flex-direction:column;align-items:center}#provesrc-notification-container .bubble-body.social-type .bubble-description.four-icons,#provesrc-notification-container .bubble-body.social-type .bubble-description.three-icons,#provesrc-notification-container .bubble-body.social-type .bubble-description.two-icons{padding-left:0!important;padding-right:0!important}#provesrc-notification-container img{opacity:1!important}"
			};
			return _psWidgetStyles.defaultStyle;
		}

//Improved form lookup
		function formsDriller(doc) {
			// if (doc) {
			// 	provesrc.debug("[formsDriller]: Running formsDriller on an IFRAME");
			// }
			var inDoc = doc || document;
			var inputs = inDoc.querySelectorAll("input:not([type=hidden]):not([" + provesrc.constants.psElements.emailField + "]), textarea:not([type=hidden])");

			provesrc.debug("Running formsDriller on", inputs.length, "inputs");
			if (inputs && inputs.length > 0) {
				for (var i = 0; i < inputs.length; i++) {
					var input = inputs[i];
					if (shouldTrackField(provesrc.constants.psElements.firstName, input)) {
						provesrc.debug("[formsDriller]: Tracked firstName field:", input);
					} else if (shouldTrackField(provesrc.constants.psElements.lastName, input)) {
						provesrc.debug("[formsDriller]: Tracked lastName field:", input);
					} else if (shouldTrackField(provesrc.constants.psElements.name, input)) {
						provesrc.debug("[formsDriller]: Tracked name (firstName) field:", input);
					} else if (shouldTrackField(provesrc.constants.psElements.emailField, input)) {
						provesrc.debug("[formsDriller]: Tracked email field:", input);
						var containerElem = input.parentNode;
						var trackedButtons = containerElem.querySelectorAll("[" + provesrc.constants.psElements.submit + "]");
						provesrc.debug("[formsDriller]: containerElem:", containerElem);
						provesrc.debug("[formsDriller]: trackedButtons:", trackedButtons);

						if (!trackedButtons || trackedButtons.length == 0) {
							setupSubmitButtonListener(containerElem, provesrc.formTypes.regular.submit);
						}
					}
				}
				debugTrackedElements(inDoc);
			}

		}

		function searchFormsWithClass(cls, inDocument, opts) {
			var forms = inDocument.querySelectorAll(cls);
			if (forms.length > 0) {
				provesrc.debug("[searchFormsWithClass] √ Found", forms.length, opts.type, "forms on page with class:", cls);
			}
			for (var i = 0; i < forms.length; i++) {
				var form = forms[i];
				var shouldIgnoreForm = form.hasAttribute(provesrc.constants.psElements.ignoreForm);
				if (!shouldIgnoreForm) {
					if (formHasEmailField(form)) {
						provesrc.debug("[searchFormsWithClass] √ Found", opts.type, "form with email field:", form);
						setupFormListeners(form, opts.type, opts.typeSubmit);
					}
				} else {
					provesrc.debug("[searchFormsWithClass] X Ignoring form:", form, "because of ", provesrc.constants.psElements.ignoreForm);
				}
			}
			provesrc.debug("[searchFormsWithClass] √ Scanned all", opts.type, "forms in page");
		}

		function shouldScanIframe(d) {
			var shouldScan = true;
			var iframeURL = d;

			try {
				iframeURL = new URL(d)
			} catch (e) {
				provesrc.debug("[shouldScanIframe] Can't check iframe url", d);
				return false;
			}

			if (iframeURL.host.indexOf(window.location.hostname) === -1) {
				shouldScan = false;
			}
			if (d.indexOf("facebook.com") > -1) {
				shouldScan = false;
			}

			return shouldScan;
		}

		function handleIframe(iframe, opts, cls) {
			provesrc.debug("[searchPageForms handleIframe]:", iframe);
			try {
				var iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
				if (iframeDocument) {
					provesrc.debug("|-----> √ iFrame document is accessible:", iframe.src);
					var iframeReadyStateCheck = setInterval(function () {
						provesrc.debug("[searchPageForms handleIframe readyState]:", iframeDocument.readyState);
						if (iframeDocument.readyState == "complete" || iframeDocument.readyState == "interactive") {
							clearInterval(iframeReadyStateCheck);
							provesrc.debug("[handleIframe]: Clearing iframeReadyStateCheck");
							provesrc.debug("[handleIframe]: DOM fully loaded and parsed, searching forms");
							searchFormsWithClass(cls, iframeDocument, opts);
							formsDriller(iframeDocument);
							// provesrc.DOM.observe(iframeDocument.body, true);
						}
					}, 250);
				} else {
					provesrc.debug("|----- X Couldn't find window.document for iFrame");
				}
			} catch (err) {
				provesrc.debug("|----- X iFrame access document error (CORS):", iframe.src, "ERROR:", err);
			}
		}

		function searchPageForms() {

			for (var type in provesrc.formTypes) {
				provesrc.debug("@----------> Searching form type:", type);
				var typeForms = provesrc.formTypes[type].forms;
				var typeSubmit = provesrc.formTypes[type].submit;
				var opts = {typeForms: typeForms, typeSubmit: typeSubmit, type: type};

				for (var i = 0; i < typeForms.length; i++) {
					var cls = typeForms[i];
					if (type == 'iframe' && !provesrc.utils.isIE()) {
						provesrc.debug("|-----> Looking for iFrames in page");
						var iframes = document.getElementsByTagName(type);
						if (iframes && iframes.length > 0) {
							provesrc.debug("|-----> √ Found", iframes.length, type, "on page");
							for (var m = 0; m < iframes.length; m++) {
								var iframe = iframes[m];
								if (iframe.src) {
									if (shouldScanIframe(iframe.src)) {
										try {
											handleIframe(iframe, opts, cls)
										} catch (ex) {
											provesrc.debug("|----- X iFrame access document error (CORS):", ex);
										}
									} else {
										provesrc.debug("|----- X iFrame SRC doesn't contain hostname", window.location.hostname);
									}
								} else {
									handleIframe(iframe, opts, cls)
								}
							}
						} else {
							provesrc.debug("|----- X No Iframes in document");
						}
					} else {
						searchFormsWithClass(cls, document, opts);
					}
				}
			}

		}

		function setupFormListeners(form, type, submitButtons) {
			provesrc.debug("[setupFormListeners] Setting up listeners on form", form, "with type", type, "buttons", submitButtons);
			var psFormId = form.getAttribute(provesrc.constants.psElements.form);
			var patchedSelectorFn = null;
			if (form.querySelectorAll.toString().indexOf('[native code]') < 0) {
				patchedSelectorFn = form.querySelectorAll;
				delete form.querySelectorAll;
			}
			var trackedButtons = form.querySelectorAll("[" + provesrc.constants.psElements.submit + "]");
			var existingButtons = form.querySelectorAll(submitButtons);
			provesrc.debug("[setupFormListeners] form", form, "trackedButtons:", trackedButtons);
			provesrc.debug("[setupFormListeners] form", form, "existingButtons:", existingButtons);

			var existsCounter = 0;

			if (existingButtons && existingButtons.length > 0) {
				for (var i = 0; i < existingButtons.length; i++) {
					var fbtn = existingButtons[i];
					var btnListenerId = fbtn.getAttribute("ps-submit-button-id");
					if (btnListenerId && provesrc.URL.listeners.isTracked(btnListenerId)) {
						existsCounter++
					}
				}
			}

			if (!psFormId && !provesrc.URL.listeners.isTracked(psFormId)) {
				provesrc.debug("[setupFormListeners] Adding listener to", type, "form:", form);
				if (form.attachEvent) { //IE
					form.attachEvent("submit", function (e) {
						return processSubmitEvent(e, form);
					});
				} else {
					form.addEventListener("submit", function (e) {
						return processSubmitEvent(e, form);
					});
				}
			}
			if (!trackedButtons || trackedButtons.length == 0 || !provesrc.session.didScanForms || (existsCounter == 0 && existingButtons && existingButtons.length > 0)) {
				provesrc.session.didScanForms = true;
				provesrc.debug("[setupFormListeners] No tracked buttons in this form, trying to find some...", form);
				setupSubmitButtonListener(form, submitButtons);
			}
			if (patchedSelectorFn) {
				form.querySelectorAll = patchedSelectorFn;
			}
		}


		function setupSubmitButtonListener(form, submitButtons) {
			var foundFormSubmit = false;
			var formSubmitButton = null;
			var possibleButtons = ["button[type=submit]", "input[type=submit]", "div[onclick]", "a[onclick]", "input[type=button]", "button"].join(", ");
			provesrc.debug("[setupSubmitButtonListener] submitButtons:", submitButtons);
			provesrc.debug("[setupSubmitButtonListener] Checking Form:", form);

			for (var idx = 0; idx < submitButtons.length; idx++) {
				var submitBtn = submitButtons[idx];
				provesrc.debug("[setupSubmitButtonListener] Checking submit button:", submitBtn);
				if (!formSubmitButton || formSubmitButton.length == 0) {
					// provesrc.debug("|----- Searching submit button:", submitBtn);
					formSubmitButton = form.querySelectorAll(submitBtn);
					if (formSubmitButton && formSubmitButton.length > 0) {
						foundFormSubmit = true;
						provesrc.debug("[setupSubmitButtonListener] √ Found form submit button:", submitBtn);
					} else {
						formSubmitButton = null;
						foundFormSubmit = false;
						// provesrc.debug("|--- X Not Found:", submitBtn);
					}
				}
			}

			if (!foundFormSubmit) {
				provesrc.debug("[setupSubmitButtonListener] Submit button not found, searching possibleButtons");
				formSubmitButton = form.querySelectorAll(possibleButtons);
				if (!formSubmitButton || (formSubmitButton && formSubmitButton.length == 0)) {
					formSubmitButton = form.querySelectorAll("a");
				}
				if (formSubmitButton && formSubmitButton.length == 1) {
					provesrc.debug("|--- √ Found a single default form button (not submit)", formSubmitButton);
				}
			}

			if (formSubmitButton && formSubmitButton.length > 0) {
				provesrc.debug("[setupSubmitButtonListener] formSubmitButton.length > 0", formSubmitButton);
				for (var b = 0; b < formSubmitButton.length; b++) {
					var submitButton = formSubmitButton[b];
					provesrc.debug("[setupSubmitButtonListener] Checking submitButton:", submitButton);
					if (submitButton.attachEvent) { //IE
						submitButton.attachEvent("click", function (e) {
							processSubmitEvent(e, form);
						});
						form.attachEvent("keydown", function (event) {
							if (event.key === "Enter") {
								provesrc.debug("[processSubmitEvent] ENTER key pressed on form", form);
								processSubmitEvent(event, form);
							}
						});
					} else {
						submitButton.addEventListener("click", function (e) {
							provesrc.debug("[processSubmitEvent] Button clicked on in", form, e);
							processSubmitEvent(e, form);
						});
						form.addEventListener("keydown", function (event) {
							if (event.key === "Enter") {
								provesrc.debug("[processSubmitEvent] ENTER key pressed on form", form);
								processSubmitEvent(event, form);
							}
						});
					}
					provesrc.debug("|--- √ Setup listener on submit button", submitButton);
					var btnId = provesrc.utils.UUID();
					provesrc.URL.listeners.track(btnId);
					submitButton.setAttribute(provesrc.constants.psElements.submit, btnId);
				}
			}

			if (!formSubmitButton || formSubmitButton.length == 0) {
				if (form.parentNode) {
					setupSubmitButtonListener(form.parentNode, submitButtons)
				}
			} else {
				//Set internal attribute to track scanning
				provesrc.debug("|--- √ Found form container", form);
				var formId = provesrc.utils.UUID();
				provesrc.URL.listeners.track(formId);
				form.setAttribute(provesrc.constants.psElements.form, formId);
			}
		}

		function debugTrackedElements(doc) {
			var inDoc = doc || document;
			if (provesrc.settings.debugMode || provesrc.settings.dashboardDebugMode) {
				provesrc.debug("[Debug Mode] |--- √ Debug Mode is ON, showing tracked elements");
				var trackedElements = inDoc.querySelectorAll("[ps-email-field-id], [ps-dynamic-form-id], [ps-submit-button-id], [ps-firstname-field-id], [ps-lastname-field-id], [ps-name-field-id]");
				if (trackedElements && trackedElements.length > 0) {
					for (var i = 0; i < trackedElements.length; i++) {
						var elem = trackedElements[i];
						elem.style.setProperty('border', "2px dashed red", 'important');
					}
				}
			}
		}

		function formHasEmailField(form) {
			var inputFields = form.getElementsByTagName("input");
			var emailFieldSelectedQuery = "input[type=email]"
			var customTracking = provesrc.settings.customFormTracking;
			if (customTracking && customTracking.email.length > 0) {
				emailFieldSelectedQuery = emailFieldSelectedQuery + "," + customTracking.email.join();
			}
			var emailField = form.querySelectorAll(emailFieldSelectedQuery);

			if (emailField && emailField.length > 0) {
				var emailF = emailField[0];
				emailF.setAttribute(provesrc.constants.psElements.emailField, provesrc.utils.UUID());
				return true;
			} else {
				if (inputFields) {
					for (var x = 0; x < inputFields.length; x++) {
						var field = inputFields[x];
						if (field && field.attributes) {
							if (shouldTrackField(provesrc.constants.psElements.emailField, field, form)) {
								provesrc.debug("|--- √ Found email field:", field);
								return true;
							} else {
								provesrc.debug("|--- X No email field");
							}
						}
					}
				}
			}
			return false;
		}

		function shouldTrackField(type, field, form) {
			var isValid = false;
			var inputName = field.getAttribute("name");
			var inputId = field.getAttribute("id");
			var inputPlaceholder = field.getAttribute("placeholder");
			var inputClass = field.getAttribute("class");
			var fieldString = field.outerHTML.toLowerCase();

			if (type && field) {
				if (type == provesrc.constants.psElements.emailField) {
					var emailRegex = provesrc.constants.fieldRegex.email;

					// if (field) { //Check if upper level already has an email field tracked
					// 	if (field.closest) {
					// 		var closestFormElem = field.closest("[ps-dynamic-form-id]");
					// 		if (closestFormElem) {
					// 			var additionalEmailFields = closestFormElem.querySelectorAll("[ps-email-field-id]");
					// 			if (additionalEmailFields && additionalEmailFields.length > 0) {
					// 				provesrc.debug("[***] Closest element", closestFormElem, "has a tracked email field:", additionalEmailFields);
					// 				return;
					// 			}
					// 		}
					// 	} else if (field.parentNode && field.parentNode.querySelectorAll("[ps-email-field-id]").length > 0) {
					// 		return;
					// 	}
					// }

					if (field && field.tagName
						&& field.tagName.toLowerCase() == "input"
						&& field.type == "email") {
						isValid = true;
					} else if ((field && field.tagName
							&& field.tagName.toLowerCase() == "input")
						&& (field.type == "email"
							|| (inputName && inputName.match(emailRegex) && inputName.match(emailRegex).length > 0)
							|| (inputId && inputId.match(emailRegex) && inputId.match(emailRegex).length > 0))) {
						isValid = true;
					} else if (field && inputPlaceholder && inputPlaceholder.match(emailRegex) && inputPlaceholder.match(emailRegex).length > 0) {
						isValid = true;
					} else if (fieldString.indexOf("email") > -1 || fieldString.indexOf("e-mail") > -1) {
						isValid = true;
					} else {
						if (inputId && form) {
							try {
								var emailLabel = form.querySelectorAll("label[for='" + inputId + "']");
								if (emailLabel && emailLabel.length > 0) {
									var match = emailLabel[0].textContent.match(emailRegex);
									if (match && match.length > 0) {
										provesrc.debug("|--- √ Found 'email' label that belongs to an input field");
										isValid = true;
									}
								}
							} catch (ex) {
								provesrc.debug(ex);
							}
						}
					}

					if (isValid) {
						field.setAttribute(provesrc.constants.psElements.emailField, provesrc.utils.UUID());
					}
				} else if (type == provesrc.constants.psElements.firstName) {
					var firstNameRegex = provesrc.constants.fieldRegex.firstName;

					if ((field && field.tagName
							&& (field.tagName.toLowerCase() == "input" || field.tagName.toLowerCase() == "textarea"))
						&& field.type == "text" &&
						(inputName && inputName.match(firstNameRegex) && inputName.match(firstNameRegex).length > 0)
						|| (inputId && (inputId.match(firstNameRegex) && inputId.match(firstNameRegex).length > 0) || inputId == 'name')) {
						isValid = true;
					} else if (field && inputPlaceholder && inputPlaceholder.match(firstNameRegex) && inputPlaceholder.match(firstNameRegex).length > 0) {
						isValid = true;
					} else if (field && inputClass && inputClass.match(firstNameRegex) && inputClass.match(firstNameRegex).length > 0) {
						isValid = true;
					} else if (fieldString && fieldString.match(firstNameRegex) && fieldString.match(firstNameRegex).length > 0) {
						isValid = true;
					}

					if (isValid) {
						field.setAttribute(provesrc.constants.psElements.firstName, provesrc.utils.UUID());
					}
				} else if (type == provesrc.constants.psElements.lastName) {
					var lastNameRegex = provesrc.constants.fieldRegex.lastName;

					if ((field && field.tagName
							&& (field.tagName.toLowerCase() == "input" || field.tagName.toLowerCase() == "textarea"))
						&& field.type == "text" &&
						(inputName && inputName.match(lastNameRegex) && inputName.match(lastNameRegex).length > 0)
						|| (inputId && inputId.match(lastNameRegex) && inputId.match(lastNameRegex).length > 0)) {
						isValid = true;
					} else if (field && inputPlaceholder && inputPlaceholder.match(lastNameRegex) && inputPlaceholder.match(lastNameRegex).length > 0) {
						isValid = true;
					} else if (field && inputClass && inputClass.match(lastNameRegex) && inputClass.match(lastNameRegex).length > 0) {
						isValid = true;
					} else if (fieldString && fieldString.match(lastNameRegex) && fieldString.match(lastNameRegex).length > 0) {
						isValid = true;
					}

					if (isValid) {
						field.setAttribute(provesrc.constants.psElements.lastName, provesrc.utils.UUID());
					}
				} else if (type == provesrc.constants.psElements.name) {

					if ((field && field.tagName && (field.tagName.toLowerCase() == "input" || field.tagName.toLowerCase() == "textarea"))
						&& field.type == "text"
						&& ((inputName && inputName.toLowerCase() == "name") || (inputPlaceholder && inputPlaceholder.toLowerCase() == "name"))) {
						isValid = true;
					} else if (field.className && field.className.toLowerCase() == "name") {
						isValid = true;
					} else if (inputPlaceholder && inputPlaceholder.toLowerCase().indexOf("name") > -1) {
						isValid = true;
					} else if (field && inputClass && inputClass.toLowerCase().indexOf("name") > -1) {
						isValid = true;
					}

					if (isValid) {
						field.setAttribute(provesrc.constants.psElements.name, provesrc.utils.UUID());
					}

				}
			}

			return isValid;
		}

		function sendConversions() {
			var conversions = provesrc.storage.searchKey(provesrc.constants.storageConversionsKey);
			if (conversions && conversions.length > 0) {
				for (var idx = 0; idx < conversions.length; idx++) {
					var url = conversions[idx];
					if (url.val !== "true") {
						url.val = JSON.parse(url.val);
						provesrc.debug("Reporting conversion for url", url.key, "timestamp=", url.val.timestamp, "email=", url.val.email);
						trackConversion(url);
					} else {
						provesrc.storage.del(getConversionKey(url.key));
						provesrc.debug("Removing reported conversion for url", url.key);
					}
				}
			} else {
				provesrc.debug("No conversions to report");
			}
		}

		function manuallyReportConversion(formDetails) {
			provesrc.debug("[manuallyReportConversion] Reporting conversion event with form submission:", formDetails);
			provesrc.API.req(provesrc.API.endpoints.trackFormEvent, 'POST', formDetails, function (err, res, data) {
				if (!err) {
					provesrc.debug("Reported conversion with details:", formDetails);
				}
			});
		}

		function trackConversion(url) {
			var formDetails = {};
			var psConversions = provesrc.ANALYTICS.conversionEvents.getEvents();
			formDetails.timestamp = parseInt(url.val.timestamp);
			formDetails.email = url.val.email;
			formDetails.url = url.key;
			url.val.submissionId ? formDetails.submissionId = url.val.submissionId : provesrc.utils.UUID();
			url.val.firstName ? formDetails.firstName = url.val.firstName : null;
			url.val.lastName ? formDetails.lastName = url.val.lastName : null;
			formDetails = merge(formDetails, psConversions);

			provesrc.debug("Reporting conversion events with form submission:", psConversions);
			provesrc.API.req(provesrc.API.endpoints.trackFormEvent, 'POST', formDetails, function (err, res, data) {
				if (!err) {
					provesrc.storage.set(getConversionKey(url.key), true);
					provesrc.debug("Reported conversion for url", url.key, "with details:", formDetails);
					provesrc.storage.del(getConversionKey(url.key));
					provesrc.ANALYTICS.conversionEvents.markReported(psConversions);
				}
			});
		}

		function getConversionKey(itm) {
			return provesrc.constants.storageConversionsKey + "." + itm;
		}

		function getFieldValue(form, type) {
			var elements = form.querySelectorAll("input[" + type + "], textarea[" + type + "]");
			for (var i = 0; i < elements.length; i++) {
				var elem = elements[i];
				if (elem && elem.value && elem.value.length > 0) {
					provesrc.debug("Found field of type:", type, "with value:", elem.value);
					return elem.value;
				}
			}
		}

		function processSubmitEvent(e, form) {
			var formDetails = {};
			var formId = form.getAttribute(provesrc.constants.psElements.form);
			var firstNameField = getFieldValue(form, provesrc.constants.psElements.firstName);
			var lastNameField = getFieldValue(form, provesrc.constants.psElements.lastName);
			var nameField = getFieldValue(form, provesrc.constants.psElements.name);
			var emailField = form.querySelectorAll("input[" + provesrc.constants.psElements.emailField + "], textarea[" + provesrc.constants.psElements.emailField + "]");
			var formSubmitBtn = form.querySelector("[" + provesrc.constants.psElements.submit + "]");

			if (formSubmitBtn) {
				formDetails.submissionId = formSubmitBtn.getAttribute(provesrc.constants.psElements.submit)
			}

			provesrc.debug("|--- √ Got form submit event on form:", formId);
			provesrc.debug("|--- √ Found submit button:", formSubmitBtn);
			provesrc.debug("|--- √ Submission ID:", formSubmitBtn, formDetails.submissionId);
			provesrc.debug("Email form field:", emailField);

			if (firstNameField) {
				provesrc.debug("First name form field:", firstNameField);
				formDetails.firstName = firstNameField;
			}
			if (lastNameField) {
				provesrc.debug("Last name form field:", lastNameField);
				formDetails.lastName = lastNameField;
			}
			if (nameField && (!firstNameField || firstNameField.length == 0)) {
				provesrc.debug("Name form field:", nameField);
				formDetails.firstName = nameField;
			}

			if (emailField && emailField.length > 0) {
				provesrc.debug("|--- √ Checking", emailField.length, "potential email forms");

				for (var i = 0; i < emailField.length; i++) {
					provesrc.debug("|--- √ Checking email field #", i, "out of", emailField.length);

					var email = emailField[i];
					var reValidEmail = new RegExp(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/);

					if (reValidEmail.test(email.value)) {
						formDetails.email = email.value;
						break;
					}
				}

				if (formDetails.email) {
					provesrc.debug("|--- √ Capturing conversion on form", formId);
					provesrc.debug("|--- √ Valid email:", email.value);

					var currentURL = proofsrc.URL.getCurrentURL();
					var conversion = provesrc.storage.get(getConversionKey(currentURL));
					formDetails.timestamp = Date.now();
					provesrc.debug("|--- √ Form Details:", formDetails);

					if (getLocalStorage()) {
						if (!conversion) {
							provesrc.storage.set(getConversionKey(currentURL), formDetails, true);
							sendConversions();
						} else {
							provesrc.debug("Conversion for URL", currentURL, "still exists on LS");
						}
					} else {
						formDetails.url = currentURL;
						manuallyReportConversion(formDetails);
					}
				} else {
					provesrc.debug("|--- X Email(s) in form", formId, "is not a valid email address");
				}

			}

			return false;
		}

		function DOMChangeEventCallback(mutations) {
			var shouldSetupListeners = false;
			if (mutations && mutations.length > 0) {
				if (!isPSWidget(mutations)) {
					// provesrc.debug("[Mutation Observer]: DOM has changed - scanning changes...");
					for (var i = 0; i < mutations.length; i++) {
						var mutation = mutations[i];
						if (mutation.addedNodes && mutation.addedNodes.length > 0) {
							for (var x = 0; x < mutation.addedNodes.length; x++) {
								var addedNode = mutation.addedNodes[x];
								if (mutationContainsForm(addedNode)) {
									shouldSetupListeners = true;
								}
							}
						}
						if (mutation.removedNodes && mutation.removedNodes.length > 0) {
							for (var y = 0; y < mutation.removedNodes.length; y++) {
								var removedNode = mutation.removedNodes[y];
								if (mutationContainsForm(removedNode)) {
									shouldSetupListeners = true;
								}
							}
						}
					}

					if (shouldSetupListeners) {
						provesrc.URL.setupFormsSubmissionListener();
						provesrc.debug("[Mutation Observer]: Changes contain form, running listeners");
					}
				}
			} else {
				provesrc.URL.setupFormsSubmissionListener();
			}
		}

		function mutationContainsForm(n) {
			var formRegex = /(form|iframe)/ig;
			var submitRegex = /type="submit"/ig;
			if (n.outerHTML && n.outerHTML.match(formRegex) && n.outerHTML.match(formRegex).length > 0) return true;
			else if (n.innerHTML && n.innerHTML.match(formRegex) && n.innerHTML.match(formRegex).length > 0) return true;
			else if (n.innerText && n.innerText.match(formRegex) && n.innerText.match(formRegex).length > 0) return true;
			else if (n.outerHTML && n.outerHTML.match(submitRegex) && n.outerHTML.match(submitRegex).length > 0) return true;
			else if (n.innerHTML && n.innerHTML.match(submitRegex) && n.innerHTML.match(submitRegex).length > 0) return true;
			else if (n.innerText && n.innerText.match(submitRegex) && n.innerText.match(submitRegex).length > 0) return true;
			else if (n.tagName && n.tagName.toLowerCase() == "iframe") return true;
			else if (n.tagName && n.tagName.toLowerCase() == "form") return true;
			else return false;
		}


		function shuffleNotifications(nArray) {
			for (var i = nArray.length - 1; i > 0; i--) {
				var j = Math.floor(Math.random() * (i + 1));
				var temp = nArray[i];
				nArray[i] = nArray[j];
				nArray[j] = temp;
			}
			return nArray;
		}

		function filterNotifications(nArray) {
			provesrc.debug("[filterNotifications] Filtering notifications...");
			var newNArray = [];
			for (var i = 0; nArray.length > i; i++) {
				var notification = nArray[i];
				if ((notification.settings.sessionShowOnce && !provesrc.session.storage.didViewNotification(notification, true)) || !notification.settings.sessionShowOnce) {
					newNArray.push(notification);
				}
			}
			return newNArray;
		}

		function isPSWidget(mutations) {
			if (mutations.length == 1) {
				var n = mutations[0];
				var provesrcWidget = "provesrc-widget-area";
				var psMutation = null;
				if (n.addedNodes && n.addedNodes.length == 1) {
					psMutation = n.addedNodes[0];
				} else if (n.removedNodes && n.removedNodes.length == 1) {
					psMutation = n.removedNodes[0];
				}
				return (psMutation && psMutation.outerHTML && psMutation.outerHTML.indexOf(provesrcWidget) > -1)
			}
			return false;
		}

		function getColorContrast(color, darkerPercent, lighterPercent) {
			return provesrc.utils.isColorLight(color) ? provesrc.utils.getDarkerColor(color, darkerPercent) : provesrc.utils.getLighterColor(color, lighterPercent);
		}
	}
).call(this);

//snarkdown
window.snarkdown = function() {
	function n(n) {
		return n && n.replace(RegExp("^" + (n.match(/^(\t| )+/) || "")[0], "gm"), "")
	}

	function e(n) {
		return n && n.replace(/"/g, "&quot;")
	}

	var r = {
		_: ["<em>", "</em>"],
		__: ["<strong>", "</strong>"],
		"\n\n": ["<br />"],
		">": ["<blockquote>", "</blockquote>"],
		"*": ["<ul>", "</ul>"],
		"#": ["<ol>", "</ol>"]
	};
	return function t(o) {
		function c(n) {
			var e = n.replace(/\*/g, "_").replace(/^( {2}\n\n*|\n{2,})/g, "\n\n"), t = m[m.length - 1] === n, o = r[e];
			return o ? o[1] ? (m[t ? "pop" : "push"](n), o[t ? 1 : 0]) : o[0] : n
		}

		function a() {
			for(var n = "", e = m.length; e--;) n += c(m[e]);
			return n
		}

		var l, u, g, s, p,
			i = /(?:^```(\w*)\n([\s\S]*?)\n```$)|((?:(?:^|\n+)(?:\t|  {2,}).+)+\n*)|((?:(?:^|\n)([>*+-]|\d+\.)\s+.*)+)|(?:\!\[([^\]]*?)\]\(([^\)]+?)\))|(\[)|(\](?:\(([^\)]+?)\))?)|(?:(?:^|\n+)([^\s].*)\n(\-{3,}|={3,})(?:\n+|$))|(?:(?:^|\n+)(#{1,3})\s*(.+)(?:\n+|$))|(?:`([^`].*?)`)|(  \n\n*|\n{2,}|__|\*\*|[_*])/gm,
			m = [], f = "", h = 0, d = {};
		for(o = o.replace(/^\n+|\n+$/g, "").replace(/^\[(.+?)\]:\s*(.+)$/gm, function(n, e, r) {
			return d[e.toLowerCase()] = r, ""
		}); g = i.exec(o);) u = o.substring(h, g.index), h = i.lastIndex, l = g[0], u.match(/[^\\](\\\\)*\\$/) || (g[2] || g[3] ? l = '<pre class="code ' + (g[3] ? "poetry" : g[1].toLowerCase()) + '">' + n((g[2] || g[3]).replace(/^\n+|\n+$/g, "")) + "</pre>" : g[5] ? ("." === (p = g[5]).charAt(p.length - 1) && (p = ".", g[4] = g[4].replace(/^\d+/gm, "")), s = t(n(g[4].replace(/^\s*[>*+.-]/gm, ""))), ">" !== p && (p = "." === p ? "#" : "*", s = s.replace(/^(.*)(\n|$)/gm, "<li>$1</li>")), l = r[p][0] + s + r[p][1]) : g[7] ? l = '<img src="' + e(g[7]) + '" alt="' + e(g[6]) + '">' : g[9] ? (f = f.replace("<a>", '<a onclick="(function(e){e.stopPropagation();})(event)" href="' + e(g[10] || d[u.toLowerCase()]) + '">'), l = a() + "</a>") : g[8] ? l = "<a>" : g[11] || g[13] ? l = "<" + (p = "h" + (g[13] ? g[13].length : "=" === g[12][0] ? 1 : 2)) + ">" + t(g[11] || g[14]) + "</" + p + ">" : g[15] ? l = "<code>" + g[15] + "</code>" : g[16] && (l = c(g[16]))), f += u, f += l;
		return (f + o.substring(h) + a()).trim()
	}


}();

//CountUp.js
!function(a, n) {
	"function" == "object" == typeof exports ? module.exports = n(require, exports, module) : a.CountUp = n()
}(this, function(a, n, t) {
	return function(a, n, t, e, i, r) {
		var u = this;
		if(u.version = function() {
			return "1.9.3"
		}, u.options = {
			useEasing: !0,
			useGrouping: !0,
			separator: ",",
			decimal: ".",
			easingFn: function(a, n, t, e) {
				return t * (1 - Math.pow(2, -10 * a / e)) * 1024 / 1023 + n
			},
			formattingFn: function(a) {
				var n, t, e, i, r, o, s = a < 0;
				if(a = Math.abs(a).toFixed(u.decimals), n = (a += "").split("."), t = n[0], e = 1 < n.length ? u.options.decimal + n[1] : "", u.options.useGrouping) {
					for(i = "", r = 0, o = t.length; r < o; ++r) 0 !== r && r % 3 == 0 && (i = u.options.separator + i), i = t[o - r - 1] + i;
					t = i
				}
				return u.options.numerals.length && (t = t.replace(/[0-9]/g, function(a) {
					return u.options.numerals[+a]
				}), e = e.replace(/[0-9]/g, function(a) {
					return u.options.numerals[+a]
				})), (s ? "-" : "") + u.options.prefix + t + e + u.options.suffix
			},
			prefix: "",
			suffix: "",
			numerals: []
		}, r && "object" == typeof r) for(var o in u.options) r.hasOwnProperty(o) && null !== r[o] && (u.options[o] = r[o]);
		"" === u.options.separator ? u.options.useGrouping = !1 : u.options.separator = "" + u.options.separator;
		for(var s = 0, l = ["webkit", "moz", "ms", "o"], m = 0; m < l.length && !window.requestAnimationFrame; ++m) window.requestAnimationFrame = window[l[m] + "RequestAnimationFrame"], window.cancelAnimationFrame = window[l[m] + "CancelAnimationFrame"] || window[l[m] + "CancelRequestAnimationFrame"];

		function d(a) {
			return "number" == typeof a && !isNaN(a)
		}

		window.requestAnimationFrame || (window.requestAnimationFrame = function(a, n) {
			var t = (new Date).getTime(), e = Math.max(0, 16 - (t - s)), i = window.setTimeout(function() {
				a(t + e)
			}, e);
			return s = t + e, i
		}), window.cancelAnimationFrame || (window.cancelAnimationFrame = function(a) {
			clearTimeout(a)
		}), u.initialize = function() {
			return !!u.initialized || (u.error = "", u.d = "string" == typeof a ? document.getElementById(a) : a, u.d ? (u.startVal = Number(n), u.endVal = Number(t), d(u.startVal) && d(u.endVal) ? (u.decimals = Math.max(0, e || 0), u.dec = Math.pow(10, u.decimals), u.duration = 1e3 * Number(i) || 2e3, u.countDown = u.startVal > u.endVal, u.frameVal = u.startVal, u.initialized = !0) : (u.error = "[CountUp] startVal (" + n + ") or endVal (" + t + ") is not a number", !1)) : !(u.error = "[CountUp] target is null or undefined"))
		}, u.printValue = function(a) {
			var n = u.options.formattingFn(a);
			"INPUT" === u.d.tagName ? this.d.value = n : "text" === u.d.tagName || "tspan" === u.d.tagName ? this.d.textContent = n : this.d.innerHTML = n
		}, u.count = function(a) {
			u.startTime || (u.startTime = a);
			var n = (u.timestamp = a) - u.startTime;
			u.remaining = u.duration - n, u.options.useEasing ? u.countDown ? u.frameVal = u.startVal - u.options.easingFn(n, 0, u.startVal - u.endVal, u.duration) : u.frameVal = u.options.easingFn(n, u.startVal, u.endVal - u.startVal, u.duration) : u.countDown ? u.frameVal = u.startVal - (u.startVal - u.endVal) * (n / u.duration) : u.frameVal = u.startVal + (u.endVal - u.startVal) * (n / u.duration), u.countDown ? u.frameVal = u.frameVal < u.endVal ? u.endVal : u.frameVal : u.frameVal = u.frameVal > u.endVal ? u.endVal : u.frameVal, u.frameVal = Math.round(u.frameVal * u.dec) / u.dec, u.printValue(u.frameVal), n < u.duration ? u.rAF = requestAnimationFrame(u.count) : u.callback && u.callback()
		}, u.start = function(a) {
			u.initialize() && (u.callback = a, u.rAF = requestAnimationFrame(u.count))
		}, u.pauseResume = function() {
			u.paused ? (u.paused = !1, delete u.startTime, u.duration = u.remaining, u.startVal = u.frameVal, requestAnimationFrame(u.count)) : (u.paused = !0, cancelAnimationFrame(u.rAF))
		}, u.reset = function() {
			u.paused = !1, delete u.startTime, u.initialized = !1, u.initialize() && (cancelAnimationFrame(u.rAF), u.printValue(u.startVal))
		}, u.update = function(a) {
			u.initialize() && (d(a = Number(a)) ? (u.error = "", a !== u.frameVal && (cancelAnimationFrame(u.rAF), u.paused = !1, delete u.startTime, u.startVal = u.frameVal, u.endVal = a, u.countDown = u.startVal > u.endVal, u.rAF = requestAnimationFrame(u.count))) : u.error = "[CountUp] update() - new endVal is not a number: " + a)
		}, u.initialize() && u.printValue(u.startVal)
	}
});
